{"wireMagic": "pb3", "pens": [{}], "wsWinStyles": [{}, {"mhModeHint": 2, "juJustifCode": 0, "sdScrollDir": 3}], "wpWinPositions": [{}, {"apPoint": 6, "ahHorPos": 20, "avVerPos": 100, "rcRows": 2, "ccCols": 40}, {"apPoint": 0, "ahHorPos": 20, "avVerPos": 0, "rcRows": 2, "ccCols": 40}], "events": [{"tStartMs": 0, "dDurationMs": 124670, "id": 1, "wpWinPosId": 2, "wsWinStyleId": 1}, {"tStartMs": 250, "dDurationMs": 18680, "wWinId": 1, "segs": [{"utf8": "[Hu<PERSON>ba]"}]}, {"tStartMs": 24060, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 24070, "dDurationMs": 5400, "wWinId": 1, "segs": [{"utf8": "[Hu<PERSON>ba]"}]}, {"tStartMs": 27460, "dDurationMs": 2010, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 27470, "dDurationMs": 2000, "wWinId": 1, "segs": [{"utf8": "<PERSON>", "acAsrConf": 232}]}, {"tStartMs": 39980, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 39990, "dDurationMs": 5360, "wWinId": 1, "segs": [{"utf8": "a", "acAsrConf": 202}]}, {"tStartMs": 42340, "dDurationMs": 3010, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 42350, "dDurationMs": 3000, "wWinId": 1, "segs": [{"utf8": "a", "acAsrConf": 218}]}, {"tStartMs": 45500, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 45510, "dDurationMs": 3170, "wWinId": 1, "segs": [{"utf8": "[Hu<PERSON>ba]"}]}, {"tStartMs": 50730, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 50740, "dDurationMs": 4010, "wWinId": 1, "segs": [{"utf8": "<PERSON><PERSON>", "acAsrConf": 124}]}, {"tStartMs": 52080, "dDurationMs": 2670, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 52090, "dDurationMs": 4450, "wWinId": 1, "segs": [{"utf8": "[Hu<PERSON>ba]"}]}, {"tStartMs": 54740, "dDurationMs": 1800, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 54750, "dDurationMs": 7450, "wWinId": 1, "segs": [{"utf8": "<PERSON><PERSON><PERSON><PERSON>", "acAsrConf": 137}]}, {"tStartMs": 56530, "dDurationMs": 5670, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 56540, "dDurationMs": 5660, "wWinId": 1, "segs": [{"utf8": "[Hu<PERSON>ba]"}]}, {"tStartMs": 64320, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 64330, "dDurationMs": 5169, "wWinId": 1, "segs": [{"utf8": "[Hu<PERSON>ba]"}]}, {"tStartMs": 80440, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 80450, "dDurationMs": 5959, "wWinId": 1, "segs": [{"utf8": "[Hu<PERSON>ba]"}]}, {"tStartMs": 88730, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 88740, "dDurationMs": 10690, "wWinId": 1, "segs": [{"utf8": "[Hu<PERSON>ba]"}]}, {"tStartMs": 102770, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 102780, "dDurationMs": 4690, "wWinId": 1, "segs": [{"utf8": "<PERSON><PERSON>", "acAsrConf": 235}]}, {"tStartMs": 104460, "dDurationMs": 3010, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 104470, "dDurationMs": 3000, "wWinId": 1, "segs": [{"utf8": "elastický", "acAsrConf": 200}]}, {"tStartMs": 122510, "wWinId": 1, "aAppend": 1, "segs": [{"utf8": "\n"}]}, {"tStartMs": 122520, "dDurationMs": 2150, "wWinId": 1, "segs": [{"utf8": "j<PERSON><PERSON>", "acAsrConf": 201}]}]}