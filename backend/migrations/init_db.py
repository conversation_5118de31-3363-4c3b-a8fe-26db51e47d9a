#!/usr/bin/env python3
"""
Database initialization script for Helios v1.0
Creates all necessary tables and initial data
"""

import os
import sys
from pathlib import Path

# Add backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.core.database import create_tables, drop_tables, engine
from app.core.config import settings
from sqlalchemy import text

def init_database():
    """Initialize database with all tables"""
    print("Initializing database...")
    
    # Drop existing tables if they exist
    print("Dropping existing tables...")
    drop_tables()
    
    # Create all tables
    print("Creating tables...")
    create_tables()
    
    # Verify tables were created
    with engine.connect() as conn:
        result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
        tables = [row[0] for row in result]
        
    print(f"Created tables: {', '.join(tables)}")
    print("Database initialization complete!")

def create_sample_data():
    """Create sample data for testing"""
    from sqlalchemy.orm import Session
    from app.models.project import Project
    from app.models.user_dictionary import UserDictionary
    
    print("Creating sample data...")
    
    db = Session(bind=engine)
    try:
        # Create sample project
        sample_project = Project(
            name="Sample YouTube Video",
            youtube_url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            original_language="en",
            target_language="cs",
            status="pending"
        )
        db.add(sample_project)
        
        # Create sample dictionary entries
        sample_words = [
            UserDictionary(word="AI", translation="AI", context="Technology"),
            UserDictionary(word="subtitle", translation="titulky", context="Media"),
            UserDictionary(word="correction", translation="oprava", context="Editing")
        ]
        db.add_all(sample_words)
        
        db.commit()
        print("Sample data created successfully!")
        
    except Exception as e:
        db.rollback()
        print(f"Error creating sample data: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    init_database()
    create_sample_data()