from pydantic_settings import BaseSettings
from typing import List
from pydantic import field_validator


class Settings(BaseSettings):
    # Databáze
    DATABASE_URL: str = "sqlite:///./ai_korektor.db"

    # API Keys
    OPENAI_API_KEY: str = ""
    ANTHROPIC_API_KEY: str = ""
    PERPLEXITY_API_KEY: str = ""
    OPENAI_MODEL: str = "gpt-4o-mini"

    # Server
    BACKEND_HOST: str = "0.0.0.0"
    BACKEND_PORT: int = 8000
    FRONTEND_HOST: str = "localhost"
    FRONTEND_PORT: int = 3000
    DEBUG: bool = False

    # CORS
    CORS_ORIGINS: str = "http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080"

    @field_validator('CORS_ORIGINS')
    @classmethod
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v

    # File Upload
    MAX_FILE_SIZE: int = 100000000
    UPLOAD_DIR: str = "./uploads"

    # Zpracování
    TEMP_DIR: str = "./temp"
    MAX_VIDEO_DURATION: int = 10800  # 3 hodiny v sekundách
    WHISPER_MODEL: str = "whisper-1"
    GPT_MODEL: str = "gpt-4o-mini"
    CONFIDENCE_THRESHOLD: float = 0.9

    # Logging
    LOG_LEVEL: str = "INFO"

    model_config = {"env_file": "../.env", "case_sensitive": True}


settings = Settings()