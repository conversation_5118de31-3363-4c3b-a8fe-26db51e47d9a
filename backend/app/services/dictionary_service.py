"""Dictionary service for managing user dictionary terms."""

from sqlalchemy.orm import Session
from app.models import UserDictionaryTerm as DictionaryModel
from app.schemas import UserDictionaryTerm, CreateDictionaryTermRequest
import uuid
from datetime import datetime
from typing import List


class DictionaryService:
    """Service for managing user dictionary."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_all_dictionary_terms(self) -> List[UserDictionaryTerm]:
        """Vrátí všechny termíny ze slovníku"""
        terms = self.db.query(DictionaryModel).all()
        return [UserDictionaryTerm.model_validate(term) for term in terms]
    
    def create_dictionary_term(self, term_data: CreateDictionaryTermRequest) -> UserDictionaryTerm:
        """Přidá nový termín do slovníku"""
        term = DictionaryModel(
            term_id=str(uuid.uuid4()),
            phrase=term_data.phrase,
            case_sensitive=term_data.case_sensitive,
            created_at=datetime.utcnow()
        )
        
        self.db.add(term)
        self.db.commit()
        self.db.refresh(term)
        
        return UserDictionaryTerm.model_validate(term)
    
    def delete_dictionary_term_by_id(self, term_id: str) -> bool:
        """Smaže termín ze slovníku"""
        term = self.db.query(DictionaryModel).filter(
            DictionaryModel.term_id == term_id
        ).first()
        
        if not term:
            return False
        
        self.db.delete(term)
        self.db.commit()
        return True
