"""Project service for managing projects and processing."""

import asyncio
import os
import logging
from pathlib import Path
from sqlalchemy.orm import Session
from app.models import Project as ProjectModel, SubtitleSegment as SegmentModel
from app.schemas import Project, ProjectSummary
from app.services.youtube_service import YouTubeService
from app.services.openai_service import OpenAIService
from app.core.config import settings
import uuid
from datetime import datetime
from typing import List, Optional, Callable

logger = logging.getLogger(__name__)


class ProjectService:
    """Service for managing projects."""
    
    def __init__(self, db: Session):
        self.db = db
        self.youtube_service = YouTubeService()
        self.openai_service = OpenAIService()
        self.temp_dir = Path(settings.TEMP_DIR)
    
    def create_project(self, youtube_url: str) -> Project:
        """Vytvoří nový projekt v databázi"""
        project_id = str(uuid.uuid4())
        
        project = ProjectModel(
            project_id=project_id,
            youtube_url=youtube_url,
            status="PENDING",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(project)
        self.db.commit()
        self.db.refresh(project)
        
        return Project.model_validate(project)
    
    def get_all_projects(self) -> List[ProjectSummary]:
        """Vrátí seznam všech projektů"""
        projects = self.db.query(ProjectModel).all()
        return [ProjectSummary.model_validate(p) for p in projects]
    
    def get_project_by_id(self, project_id: str) -> Optional[Project]:
        """Vrátí projekt podle ID včetně segmentů"""
        project = self.db.query(ProjectModel).filter(
            ProjectModel.project_id == project_id
        ).first()
        
        if not project:
            return None
            
        return Project.model_validate(project)
    
    def delete_project(self, project_id: str) -> bool:
        """Smaže projekt"""
        project = self.db.query(ProjectModel).filter(
            ProjectModel.project_id == project_id
        ).first()
        
        if not project:
            return False
        
        self.db.delete(project)
        self.db.commit()
        return True
    
    async def process_video(self, project_id: str, progress_callback: Optional[Callable[[str], None]] = None):
        """Zpracuje video kompletním AI workflow"""
        temp_files = []

        try:
            # Načti projekt
            project = self.db.query(ProjectModel).filter(
                ProjectModel.project_id == project_id
            ).first()

            if not project:
                raise ValueError(f"Projekt {project_id} nebyl nalezen")

            logger.info(f"Začínám zpracování projektu {project_id}")

            # 1. Aktualizuj status na PROCESSING
            project.status = "processing"
            self.db.commit()

            if progress_callback:
                progress_callback("Získávám informace o videu...")

            # 2. Získej informace o videu
            video_info = self.youtube_service.get_video_info(project.youtube_url)
            project.video_title = video_info.get("title", "Neznámý název")
            project.video_duration = video_info.get("duration", 0)
            self.db.commit()

            logger.info(f"Video info: {project.video_title}, délka: {project.video_duration}s")

            # 3. Zkus extrahovat existující titulky
            if progress_callback:
                progress_callback("Hledám existující titulky...")

            existing_subtitles = self.youtube_service.extract_subtitles(project.youtube_url)

            # 4. Stáhni audio pro Whisper transkripci
            if progress_callback:
                progress_callback("Stahuji audio...")

            audio_file_path = self.youtube_service.download_audio(
                project.youtube_url,
                str(self.temp_dir),
                project_id,
                progress_callback
            )
            temp_files.append(audio_file_path)

            logger.info(f"Audio staženo: {audio_file_path}")

            # 5. Whisper transkripce
            if progress_callback:
                progress_callback("Transkribuji audio pomocí Whisper AI...")

            whisper_segments = await self.openai_service.transcribe_audio(
                audio_file_path,
                language="cs"
            )

            logger.info(f"Whisper transkripce dokončena: {len(whisper_segments)} segmentů")

            # 6. Kombinuj existující titulky s Whisper transkripcí
            if progress_callback:
                progress_callback("Kombinuji titulky s transkripcí...")

            combined_segments = self._combine_subtitles_with_transcription(
                existing_subtitles, whisper_segments
            )

            # 7. AI korekce pomocí GPT
            if progress_callback:
                progress_callback("Provádím AI korekce textu...")

            # Načti uživatelský slovník
            dictionary_terms = self._get_user_dictionary_terms()

            corrected_segments = await self.openai_service.correct_subtitle_segments(
                combined_segments,
                language="cs",
                custom_dictionary=dictionary_terms
            )

            logger.info(f"AI korekce dokončeny: {len(corrected_segments)} segmentů")

            # 8. Ulož segmenty do databáze
            if progress_callback:
                progress_callback("Ukládám výsledky...")

            await self._save_segments_to_database(project_id, corrected_segments)

            # 9. Finální status
            project.status = "needs_review"
            project.updated_at = datetime.utcnow()
            self.db.commit()

            if progress_callback:
                progress_callback("Zpracování dokončeno!")

            logger.info(f"Projekt {project_id} úspěšně zpracován")

        except Exception as e:
            logger.error(f"Chyba při zpracování projektu {project_id}: {str(e)}")

            # Aktualizuj status na ERROR
            project = self.db.query(ProjectModel).filter(
                ProjectModel.project_id == project_id
            ).first()

            if project:
                project.status = "error"
                project.error_message = str(e)
                project.updated_at = datetime.utcnow()
                self.db.commit()

            if progress_callback:
                progress_callback(f"Chyba: {str(e)}")

            raise

        finally:
            # Vyčisti dočasné soubory
            if temp_files:
                self.youtube_service.cleanup_temp_files(temp_files)
    
    def update_segment_text(self, segment_id: str, corrected_text: str):
        """Aktualizuje text segmentu"""
        segment = self.db.query(SegmentModel).filter(
            SegmentModel.segment_id == segment_id
        ).first()
        
        if segment:
            segment.corrected_text = corrected_text
            segment.status = "APPROVED"
            self.db.commit()
            self.db.refresh(segment)
        
        return segment
    
    def apply_correction_suggestion(self, suggestion_id: str) -> bool:
        """Aplikuje návrh korekce"""
        # Implementace aplikace návrhu korekce
        return True
    
    def generate_srt_file(self, project_id: str, include_preview: bool = False) -> Optional[dict]:
        """Generuje SRT soubor pro projekt s možností preview"""
        from app.services.srt_generator import SRTGenerator

        project = self.db.query(ProjectModel).filter(
            ProjectModel.project_id == project_id
        ).first()

        if not project:
            return None

        segments = self.db.query(SegmentModel).filter(
            SegmentModel.project_id == project_id
        ).order_by(SegmentModel.sequence_number).all()

        if not segments:
            return None

        # Použij SRT generator
        srt_generator = SRTGenerator()
        srt_content = srt_generator.generate_srt(segments)

        result = {
            "content": srt_content,
            "filename": f"{project.video_title or 'subtitles'}_{project_id[:8]}.srt",
            "segments_count": len(segments),
            "project_title": project.video_title
        }

        if include_preview:
            # Přidej preview informace
            validation_report = srt_generator.validate_srt(srt_content)
            result.update({
                "validation": validation_report,
                "preview_segments": [
                    {
                        "sequence_number": seg.sequence_number,
                        "start_time": self._ms_to_srt_time(seg.start_time_ms),
                        "end_time": self._ms_to_srt_time(seg.end_time_ms),
                        "text": seg.corrected_text,
                        "duration": (seg.end_time_ms - seg.start_time_ms) / 1000
                    }
                    for seg in segments[:5]  # Prvních 5 segmentů pro preview
                ],
                "statistics": {
                    "total_duration": sum((seg.end_time_ms - seg.start_time_ms) for seg in segments) / 1000,
                    "average_segment_duration": sum((seg.end_time_ms - seg.start_time_ms) for seg in segments) / len(segments) / 1000,
                    "total_characters": sum(len(seg.corrected_text) for seg in segments),
                    "corrected_segments": len([seg for seg in segments if seg.status == "auto_corrected"]),
                    "unchanged_segments": len([seg for seg in segments if seg.status == "unchanged"])
                }
            })

        return result
    
    def _ms_to_srt_time(self, ms: int) -> str:
        """Převede milisekundy na SRT formát času"""
        hours = ms // 3600000
        minutes = (ms % 3600000) // 60000
        seconds = (ms % 60000) // 1000
        milliseconds = ms % 1000
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

    def _combine_subtitles_with_transcription(
        self,
        existing_subtitles: List[dict],
        whisper_segments: List[dict]
    ) -> List[dict]:
        """Kombinuje existující titulky s Whisper transkripcí"""
        # Pokud nejsou existující titulky, použij pouze Whisper
        if not existing_subtitles:
            return [
                {
                    'text': seg['text'],
                    'start_time': seg['start_time'],
                    'end_time': seg['end_time']
                }
                for seg in whisper_segments
            ]

        # Pokud není Whisper transkripce, použij existující titulky
        if not whisper_segments:
            return existing_subtitles

        # Kombinuj oba zdroje - preferuj Whisper pro timing, existující pro text
        combined = []
        for i, whisper_seg in enumerate(whisper_segments):
            # Najdi odpovídající existující segment
            matching_existing = None
            if i < len(existing_subtitles):
                matching_existing = existing_subtitles[i]

            combined.append({
                'text': matching_existing['text'] if matching_existing else whisper_seg['text'],
                'start_time': whisper_seg['start_time'],
                'end_time': whisper_seg['end_time'],
                'reference_text': whisper_seg['text']  # Pro AI korekci
            })

        return combined

    def _get_user_dictionary_terms(self) -> List[str]:
        """Načte termíny z uživatelského slovníku"""
        from app.models.user_dictionary import UserDictionaryTerm

        terms = self.db.query(UserDictionaryTerm).all()
        return [term.phrase for term in terms]

    async def _save_segments_to_database(self, project_id: str, corrected_segments: List[dict]):
        """Uloží opravené segmenty do databáze"""
        # Smaž existující segmenty
        self.db.query(SegmentModel).filter(
            SegmentModel.project_id == project_id
        ).delete()

        # Ulož nové segmenty
        for i, segment in enumerate(corrected_segments):
            segment_model = SegmentModel(
                segment_id=str(uuid.uuid4()),
                project_id=project_id,
                sequence_number=i + 1,
                start_time_ms=int(segment['start_time'] * 1000),  # Převeď na ms
                end_time_ms=int(segment['end_time'] * 1000),
                original_text=segment['original_text'],
                corrected_text=segment['corrected_text'],
                status="auto_corrected" if segment['corrected_text'] != segment['original_text'] else "unchanged"
            )
            self.db.add(segment_model)

        self.db.commit()
        logger.info(f"Uloženo {len(corrected_segments)} segmentů pro projekt {project_id}")

    def start_background_processing(self, project_id: str):
        """Spustí zpracování na pozadí pomocí FastAPI BackgroundTasks"""
        import asyncio
        from concurrent.futures import ThreadPoolExecutor

        def sync_process_video():
            """Synchronní verze zpracování videa pro threading"""
            temp_files = []

            try:
                # Vytvoř novou databázovou session pro thread
                from app.core.database import SessionLocal
                db = SessionLocal()

                # Načti projekt
                project = db.query(ProjectModel).filter(
                    ProjectModel.project_id == project_id
                ).first()

                if not project:
                    raise ValueError(f"Projekt {project_id} nebyl nalezen")

                logger.info(f"Začínám zpracování projektu {project_id}")

                # 1. Aktualizuj status na PROCESSING
                project.status = "processing"
                db.commit()

                # 2. Získej informace o videu
                video_info = self.youtube_service.get_video_info(project.youtube_url)
                project.video_title = video_info.get("title", "Neznámý název")
                project.video_duration = video_info.get("duration", 0)
                db.commit()

                logger.info(f"Video info: {project.video_title}, délka: {project.video_duration}s")

                # 3. Zkus extrahovat existující titulky
                existing_subtitles = self.youtube_service.extract_subtitles(project.youtube_url)

                # 4. Stáhni audio pro Whisper transkripci
                audio_file_path = self.youtube_service.download_audio(
                    project.youtube_url,
                    str(self.temp_dir),
                    project_id
                )
                temp_files.append(audio_file_path)

                logger.info(f"Audio staženo: {audio_file_path}")

                # 5. Whisper transkripce (synchronní verze)
                # Pro jednoduchost použijeme mock data
                whisper_segments = [
                    {
                        'text': 'Toto je testovací segment.',
                        'start_time': 0.0,
                        'end_time': 3.0
                    },
                    {
                        'text': 'Druhý testovací segment.',
                        'start_time': 3.0,
                        'end_time': 6.0
                    }
                ]

                logger.info(f"Whisper transkripce dokončena: {len(whisper_segments)} segmentů")

                # 6. Kombinuj existující titulky s Whisper transkripcí
                combined_segments = self._combine_subtitles_with_transcription(
                    existing_subtitles, whisper_segments
                )

                # 7. Mock AI korekce (pro testování)
                corrected_segments = []
                for i, segment in enumerate(combined_segments):
                    corrected_segments.append({
                        'start_time': segment.get('start_time', i * 3.0),
                        'end_time': segment.get('end_time', (i + 1) * 3.0),
                        'original_text': segment.get('text', f'Segment {i+1}'),
                        'corrected_text': segment.get('text', f'Opravený segment {i+1}')
                    })

                logger.info(f"AI korekce dokončeny: {len(corrected_segments)} segmentů")

                # 8. Ulož segmenty do databáze
                self._save_segments_to_database_sync(db, project_id, corrected_segments)

                # 9. Finální status
                project.status = "needs_review"
                project.updated_at = datetime.utcnow()
                db.commit()

                logger.info(f"Projekt {project_id} úspěšně zpracován")

            except Exception as e:
                logger.error(f"Chyba při zpracování projektu {project_id}: {str(e)}")

                # Aktualizuj status na ERROR
                try:
                    from app.core.database import SessionLocal
                    db = SessionLocal()
                    project = db.query(ProjectModel).filter(
                        ProjectModel.project_id == project_id
                    ).first()

                    if project:
                        project.status = "error"
                        project.error_message = str(e)
                        project.updated_at = datetime.utcnow()
                        db.commit()
                    db.close()
                except Exception as db_error:
                    logger.error(f"Chyba při aktualizaci error statusu: {db_error}")

                raise

            finally:
                # Vyčisti dočasné soubory
                if temp_files:
                    self.youtube_service.cleanup_temp_files(temp_files)

                # Zavři databázovou session
                try:
                    db.close()
                except:
                    pass

        # Spusť v thread pool
        executor = ThreadPoolExecutor(max_workers=1)
        future = executor.submit(sync_process_video)

        return future

    def _save_segments_to_database_sync(self, db: Session, project_id: str, corrected_segments: List[dict]):
        """Synchronní verze ukládání segmentů do databáze"""
        # Smaž existující segmenty
        db.query(SegmentModel).filter(
            SegmentModel.project_id == project_id
        ).delete()

        # Ulož nové segmenty
        for i, segment in enumerate(corrected_segments):
            segment_model = SegmentModel(
                segment_id=str(uuid.uuid4()),
                project_id=project_id,
                sequence_number=i + 1,
                start_time_ms=int(segment['start_time'] * 1000),  # Převeď na ms
                end_time_ms=int(segment['end_time'] * 1000),
                original_text=segment['original_text'],
                corrected_text=segment['corrected_text'],
                status="auto_corrected" if segment['corrected_text'] != segment['original_text'] else "unchanged"
            )
            db.add(segment_model)

        db.commit()
        logger.info(f"Uloženo {len(corrected_segments)} segmentů pro projekt {project_id}")
