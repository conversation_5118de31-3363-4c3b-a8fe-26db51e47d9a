"""File service for handling file operations in Helios backend."""

import os
import shutil
import logging
from pathlib import Path
from typing import Optional, List
import uuid
from datetime import datetime

from fastapi import UploadFile, HTTPException
from sqlalchemy.orm import Session

from app.core.config import settings

logger = logging.getLogger(__name__)


class FileService:
    """Service for handling file operations."""
    
    def __init__(self):
        self.base_upload_dir = Path(settings.UPLOAD_DIR)
        self.temp_dir = Path(settings.TEMP_DIR)
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure required directories exist."""
        self.base_upload_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        (self.base_upload_dir / "videos").mkdir(exist_ok=True)
        (self.base_upload_dir / "audio").mkdir(exist_ok=True)
        (self.base_upload_dir / "subtitles").mkdir(exist_ok=True)
        (self.base_upload_dir / "exports").mkdir(exist_ok=True)
    
    def get_project_dir(self, project_id: int) -> Path:
        """Get project-specific directory."""
        project_dir = self.base_upload_dir / str(project_id)
        project_dir.mkdir(parents=True, exist_ok=True)
        return project_dir
    
    def get_temp_path(self, filename: str) -> Path:
        """Get temporary file path."""
        return self.temp_dir / f"{uuid.uuid4()}_{filename}"
    
    def save_uploaded_file(
        self, 
        file: UploadFile, 
        project_id: int, 
        file_type: str
    ) -> str:
        """Save uploaded file to project directory."""
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # Validate file type
        allowed_extensions = {
            'video': ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'],
            'audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'],
            'subtitle': ['.srt', '.vtt', '.txt', '.ass', '.ssa']
        }
        
        file_extension = Path(file.filename).suffix.lower()
        if file_type not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid file type: {file_type}"
            )
        
        if file_extension not in allowed_extensions[file_type]:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid file extension: {file_extension}"
            )
        
        # Create project directory
        project_dir = self.get_project_dir(project_id)
        type_dir = project_dir / file_type
        type_dir.mkdir(exist_ok=True)
        
        # Generate unique filename
        unique_filename = f"{uuid.uuid4()}_{file.filename}"
        file_path = type_dir / unique_filename
        
        # Save file
        try:
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            logger.info(f"Saved file: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"Failed to save file: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail=f"Failed to save file: {str(e)}"
            )
    
    def save_file_from_url(
        self, 
        url: str, 
        project_id: int, 
        filename: str,
        file_type: str
    ) -> str:
        """Save file from URL to project directory."""
        import requests
        
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            project_dir = self.get_project_dir(project_id)
            type_dir = project_dir / file_type
            type_dir.mkdir(exist_ok=True)
            
            file_path = type_dir / filename
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"Downloaded file from URL: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"Failed to download file from URL: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail=f"Failed to download file: {str(e)}"
            )
    
    def get_file_path(self, file_path: str) -> Optional[Path]:
        """Get file path if it exists."""
        path = Path(file_path)
        if path.exists():
            return path
        return None
    
    def delete_file(self, file_path: str) -> bool:
        """Delete file if it exists."""
        try:
            path = Path(file_path)
            if path.exists():
                path.unlink()
                logger.info(f"Deleted file: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete file: {str(e)}")
            return False
    
    def cleanup_project_files(self, project_id: int) -> None:
        """Clean up all files for a project."""
        project_dir = self.get_project_dir(project_id)
        if project_dir.exists():
            shutil.rmtree(project_dir)
            logger.info(f"Cleaned up project files: {project_id}")
    
    def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """Clean up temporary files older than specified hours."""
        cleaned_count = 0
        current_time = datetime.now()
        
        for file_path in self.temp_dir.iterdir():
            if file_path.is_file():
                file_age = current_time - datetime.fromtimestamp(
                    file_path.stat().st_mtime
                )
                
                if file_age.total_seconds() > max_age_hours * 3600:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                    except Exception as e:
                        logger.error(f"Failed to cleanup temp file: {str(e)}")
        
        logger.info(f"Cleaned up {cleaned_count} temporary files")
        return cleaned_count
    
    def get_file_info(self, file_path: str) -> dict:
        """Get file information."""
        path = Path(file_path)
        if not path.exists():
            return {}
        
        stat = path.stat()
        return {
            "path": str(path),
            "name": path.name,
            "size": stat.st_size,
            "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
            "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
            "extension": path.suffix.lower()
        }
    
    def list_project_files(self, project_id: int) -> dict:
        """List all files for a project."""
        project_dir = self.get_project_dir(project_id)
        
        files = {
            "videos": [],
            "audio": [],
            "subtitles": [],
            "exports": []
        }
        
        for file_type in files.keys():
            type_dir = project_dir / file_type
            if type_dir.exists():
                for file_path in type_dir.iterdir():
                    if file_path.is_file():
                        files[file_type].append(self.get_file_info(str(file_path)))
        
        return files
    
    def validate_file_size(self, file: UploadFile, max_size_mb: int = 100) -> bool:
        """Validate file size."""
        max_size_bytes = max_size_mb * 1024 * 1024
        
        # Check file size by reading chunks
        size = 0
        for chunk in file.file:
            size += len(chunk)
            if size > max_size_bytes:
                return False
        
        # Reset file pointer
        file.file.seek(0)
        return True
    
    def get_export_path(self, project_id: int, filename: str) -> str:
        """Get path for export file."""
        project_dir = self.get_project_dir(project_id)
        export_dir = project_dir / "exports"
        export_dir.mkdir(exist_ok=True)
        
        return str(export_dir / filename)
    
    def create_export_file(
        self, 
        content: str, 
        project_id: int, 
        filename: str
    ) -> str:
        """Create export file with given content."""
        file_path = self.get_export_path(project_id, filename)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"Created export file: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to create export file: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail=f"Failed to create export file: {str(e)}"
            )