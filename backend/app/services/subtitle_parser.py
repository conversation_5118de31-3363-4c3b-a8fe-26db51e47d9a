"""Subtitle parser service for handling various subtitle formats."""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import timedelta

from app.models.subtitle_segment import SubtitleSegment

logger = logging.getLogger(__name__)


class SubtitleParser:
    """Service for parsing and generating subtitle files in various formats."""
    
    def __init__(self):
        self.supported_formats = ['srt', 'vtt', 'txt', 'ass', 'ssa']
    
    def parse_file(self, file_path: str, format_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Parse subtitle file and return segments."""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"Subtitle file not found: {file_path}")
            
            if format_type is None:
                format_type = file_path.suffix.lower().lstrip('.')
            
            if format_type not in self.supported_formats:
                raise ValueError(f"Unsupported format: {format_type}")
            
            content = file_path.read_text(encoding='utf-8')
            
            if format_type == 'srt':
                return self._parse_srt(content)
            elif format_type == 'vtt':
                return self._parse_vtt(content)
            elif format_type == 'txt':
                return self._parse_txt(content)
            elif format_type in ['ass', 'ssa']:
                return self._parse_ass(content)
            else:
                raise ValueError(f"Parser not implemented for format: {format_type}")
                
        except Exception as e:
            logger.error(f"Failed to parse subtitle file: {str(e)}")
            raise
    
    def _parse_srt(self, content: str) -> List[Dict[str, Any]]:
        """Parse SRT format subtitles."""
        segments = []
        
        # Split by double newlines to separate subtitle blocks
        blocks = re.split(r'\n\s*\n', content.strip())
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    # Parse timing line
                    timing_line = lines[1]
                    start_str, end_str = timing_line.split(' --> ')
                    
                    start_time = self._parse_srt_time(start_str.strip())
                    end_time = self._parse_srt_time(end_str.strip())
                    
                    # Join remaining lines as text
                    text = '\n'.join(lines[2:]).strip()
                    
                    if text:
                        segments.append({
                            'start_time': start_time,
                            'end_time': end_time,
                            'text': text,
                            'original_text': text
                        })
                        
                except (ValueError, IndexError) as e:
                    logger.warning(f"Skipping malformed SRT block: {e}")
                    continue
        
        return segments
    
    def _parse_vtt(self, content: str) -> List[Dict[str, Any]]:
        """Parse WebVTT format subtitles."""
        segments = []
        
        # Remove WEBVTT header and metadata
        lines = content.strip().split('\n')
        
        # Skip header
        start_idx = 0
        for i, line in enumerate(lines):
            if line.strip() == 'WEBVTT':
                start_idx = i + 1
                break
        
        # Parse segments
        current_segment = {}
        for line in lines[start_idx:]:
            line = line.strip()
            
            if not line or line.startswith('NOTE'):
                continue
            
            if ' --> ' in line:
                # Timing line
                start_str, end_str = line.split(' --> ')
                current_segment['start_time'] = self._parse_vtt_time(start_str.strip())
                current_segment['end_time'] = self._parse_vtt_time(end_str.strip())
                
            elif current_segment and 'start_time' in current_segment:
                # Text line
                if 'text' not in current_segment:
                    current_segment['text'] = line
                else:
                    current_segment['text'] += '\n' + line
                
                # If next line is empty or timing, save segment
                if line == lines[-1].strip() or (lines.index(line) + 1 < len(lines) and 
                                               ' --> ' in lines[lines.index(line) + 1]):
                    if current_segment.get('text'):
                        current_segment['original_text'] = current_segment['text']
                        segments.append(current_segment)
                        current_segment = {}
        
        return segments
    
    def _parse_txt(self, content: str) -> List[Dict[str, Any]]:
        """Parse plain text subtitles (assumed to be one segment per line)."""
        segments = []
        lines = content.strip().split('\n')
        
        # Estimate timing based on text length
        base_duration = 3.0  # seconds per segment
        current_time = 0.0
        
        for line in lines:
            line = line.strip()
            if line:
                duration = max(base_duration, len(line) * 0.1)  # Adjust based on text length
                segments.append({
                    'start_time': current_time,
                    'end_time': current_time + duration,
                    'text': line,
                    'original_text': line
                })
                current_time += duration + 0.5  # Add small gap between segments
        
        return segments
    
    def _parse_ass(self, content: str) -> List[Dict[str, Any]]:
        """Parse ASS/SSA format subtitles."""
        segments = []
        
        # Find Events section
        events_start = content.find('[Events]')
        if events_start == -1:
            return segments
        
        events_content = content[events_start:]
        lines = events_content.split('\n')
        
        # Find format line
        format_line = None
        for line in lines:
            if line.startswith('Format:'):
                format_line = line[7:].strip()
                break
        
        if not format_line:
            return segments
        
        # Parse each dialogue line
        for line in lines:
            if line.startswith('Dialogue:'):
                try:
                    parts = line[9:].split(',', 9)  # Split into max 10 parts
                    if len(parts) >= 10:
                        start_time = self._parse_ass_time(parts[1].strip())
                        end_time = self._parse_ass_time(parts[2].strip())
                        text = parts[9].strip()
                        
                        # Clean ASS formatting tags
                        text = re.sub(r'\{[^}]*\}', '', text)
                        text = text.replace('\\N', '\n')
                        
                        if text:
                            segments.append({
                                'start_time': start_time,
                                'end_time': end_time,
                                'text': text,
                                'original_text': text
                            })
                            
                except (ValueError, IndexError) as e:
                    logger.warning(f"Skipping malformed ASS dialogue: {e}")
                    continue
        
        return segments
    
    def generate_file(
        self,
        segments: List[Dict[str, Any]],
        format_type: str,
        output_path: Optional[str] = None
    ) -> str:
        """Generate subtitle file in specified format."""
        try:
            if format_type not in self.supported_formats:
                raise ValueError(f"Unsupported format: {format_type}")
            
            if format_type == 'srt':
                content = self._generate_srt(segments)
            elif format_type == 'vtt':
                content = self._generate_vtt(segments)
            elif format_type == 'txt':
                content = self._generate_txt(segments)
            elif format_type in ['ass', 'ssa']:
                content = self._generate_ass(segments)
            else:
                raise ValueError(f"Generator not implemented for format: {format_type}")
            
            if output_path:
                Path(output_path).write_text(content, encoding='utf-8')
                return output_path
            
            return content
            
        except Exception as e:
            logger.error(f"Failed to generate subtitle file: {str(e)}")
            raise
    
    def _generate_srt(self, segments: List[Dict[str, Any]]) -> str:
        """Generate SRT format content."""
        lines = []
        
        for i, segment in enumerate(segments, 1):
            start_time = self._format_srt_time(segment['start_time'])
            end_time = self._format_srt_time(segment['end_time'])
            text = segment.get('corrected_text', segment.get('text', ''))
            
            lines.append(str(i))
            lines.append(f"{start_time} --> {end_time}")
            lines.append(text)
            lines.append("")  # Empty line between segments
        
        return "\n".join(lines)
    
    def _generate_vtt(self, segments: List[Dict[str, Any]]) -> str:
        """Generate WebVTT format content."""
        lines = ["WEBVTT", ""]
        
        for segment in segments:
            start_time = self._format_vtt_time(segment['start_time'])
            end_time = self._format_vtt_time(segment['end_time'])
            text = segment.get('corrected_text', segment.get('text', ''))
            
            lines.append(f"{start_time} --> {end_time}")
            lines.append(text)
            lines.append("")
        
        return "\n".join(lines)
    
    def _generate_txt(self, segments: List[Dict[str, Any]]) -> str:
        """Generate plain text format content."""
        lines = []
        
        for segment in segments:
            text = segment.get('corrected_text', segment.get('text', ''))
            if text:
                lines.append(text)
        
        return "\n".join(lines)
    
    def _generate_ass(self, segments: List[Dict[str, Any]]) -> str:
        """Generate ASS format content."""
        lines = [
            "[Script Info]",
            "Title: Generated Subtitles",
            "ScriptType: v4.00+",
            "WrapStyle: 0",
            "ScaledBorderAndShadow: yes",
            "",
            "[V4+ Styles]",
            "Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding",
            "Style: Default,Arial,20,&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,2,2,10,10,10,1",
            "",
            "[Events]",
            "Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text"
        ]
        
        for segment in segments:
            start_time = self._format_ass_time(segment['start_time'])
            end_time = self._format_ass_time(segment['end_time'])
            text = segment.get('corrected_text', segment.get('text', ''))
            
            # Escape special characters
            text = text.replace('\n', '\\N')
            
            lines.append(f"Dialogue: 0,{start_time},{end_time},Default,,0,0,0,,{text}")
        
        return "\n".join(lines)
    
    def _parse_srt_time(self, time_str: str) -> float:
        """Parse SRT time format (HH:MM:SS,mmm) to seconds."""
        try:
            time_part, ms_part = time_str.split(',')
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part)
            
            return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
            
        except ValueError:
            raise ValueError(f"Invalid SRT time format: {time_str}")
    
    def _parse_vtt_time(self, time_str: str) -> float:
        """Parse WebVTT time format (HH:MM:SS.mmm) to seconds."""
        try:
            if '.' in time_str:
                time_part, ms_part = time_str.split('.')
            else:
                time_part = time_str
                ms_part = '000'
            
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part.ljust(3, '0')[:3])
            
            return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
            
        except ValueError:
            raise ValueError(f"Invalid VTT time format: {time_str}")
    
    def _parse_ass_time(self, time_str: str) -> float:
        """Parse ASS time format (H:MM:SS.cc) to seconds."""
        try:
            parts = time_str.split(':')
            if len(parts) == 3:
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds = float(parts[2])
                return hours * 3600 + minutes * 60 + seconds
            else:
                raise ValueError(f"Invalid ASS time format: {time_str}")
                
        except ValueError:
            raise ValueError(f"Invalid ASS time format: {time_str}")
    
    def _format_srt_time(self, seconds: float) -> str:
        """Format seconds to SRT time format (HH:MM:SS,mmm)."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
    
    def _format_vtt_time(self, seconds: float) -> str:
        """Format seconds to VTT time format (HH:MM:SS.mmm)."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d}.{milliseconds:03d}"
    
    def _format_ass_time(self, seconds: float) -> str:
        """Format seconds to ASS time format (H:MM:SS.cc)."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        return f"{hours}:{minutes:02d}:{secs:02.2f}"
    
    def validate_segments(self, segments: List[Dict[str, Any]]) -> List[str]:
        """Validate subtitle segments and return list of errors."""
        errors = []
        
        for i, segment in enumerate(segments):
            # Check required fields
            if 'start_time' not in segment:
                errors.append(f"Segment {i}: missing start_time")
            if 'end_time' not in segment:
                errors.append(f"Segment {i}: missing end_time")
            if 'text' not in segment:
                errors.append(f"Segment {i}: missing text")
            
            # Check time validity
            if 'start_time' in segment and 'end_time' in segment:
                if segment['start_time'] < 0:
                    errors.append(f"Segment {i}: negative start_time")
                if segment['end_time'] <= segment['start_time']:
                    errors.append(f"Segment {i}: end_time <= start_time")
            
            # Check text validity
            if 'text' in segment and not segment['text'].strip():
                errors.append(f"Segment {i}: empty text")
        
        # Check for overlapping segments
        for i in range(1, len(segments)):
            if (segments[i-1]['end_time'] > segments[i]['start_time'] and
                'start_time' in segments[i-1] and 'start_time' in segments[i]):
                errors.append(
                    f"Segments {i-1} and {i}: overlapping times"
                )
        
        return errors
    
    def merge_segments(
        self,
        segments: List[Dict[str, Any]],
        max_gap: float = 0.5,
        max_length: int = 80
    ) -> List[Dict[str, Any]]:
        """Merge adjacent segments based on gap and length criteria."""
        if not segments:
            return []
        
        merged = []
        current = segments[0].copy()
        
        for next_seg in segments[1:]:
            gap = next_seg['start_time'] - current['end_time']
            combined_length = len(current['text']) + len(next_seg['text'])
            
            if gap <= max_gap and combined_length <= max_length:
                # Merge segments
                current['text'] += ' ' + next_seg['text']
                current['end_time'] = next_seg['end_time']
            else:
                # Add current segment and start new one
                merged.append(current)
                current = next_seg.copy()
        
        merged.append(current)
        return merged
    
    def split_long_segments(
        self,
        segments: List[Dict[str, Any]],
        max_length: int = 40
    ) -> List[Dict[str, Any]]:
        """Split long segments into shorter ones."""
        split_segments = []
        
        for segment in segments:
            text = segment['text']
            if len(text) <= max_length:
                split_segments.append(segment)
                continue
            
            # Split text at sentence boundaries or word boundaries
            sentences = re.split(r'[.!?]+', text)
            if len(sentences) > 1:
                # Split by sentences
                current_text = ""
                start_time = segment['start_time']
                duration = segment['end_time'] - segment['start_time']
                
                for i, sentence in enumerate(sentences):
                    if sentence.strip():
                        if current_text:
                            current_text += ". " + sentence.strip()
                        else:
                            current_text = sentence.strip()
                        
                        if len(current_text) >= max_length or i == len(sentences) - 1:
                            # Create new segment
                            seg_duration = duration * (len(current_text) / len(text))
                            split_segments.append({
                                'start_time': start_time,
                                'end_time': start_time + seg_duration,
                                'text': current_text.strip(),
                                'original_text': current_text.strip()
                            })
                            
                            start_time += seg_duration
                            current_text = ""
            else:
                # Split by words
                words = text.split()
                if len(words) > 1:
                    current_text = ""
                    start_time = segment['start_time']
                    duration = segment['end_time'] - segment['start_time']
                    
                    for word in words:
                        if current_text:
                            test_text = current_text + " " + word
                        else:
                            test_text = word
                        
                        if len(test_text) > max_length and current_text:
                            # Create segment with current text
                            seg_duration = duration * (len(current_text) / len(text))
                            split_segments.append({
                                'start_time': start_time,
                                'end_time': start_time + seg_duration,
                                'text': current_text.strip(),
                                'original_text': current_text.strip()
                            })
                            
                            start_time += seg_duration
                            current_text = word
                        else:
                            current_text = test_text
                    
                    # Add remaining text
                    if current_text:
                        split_segments.append({
                            'start_time': start_time,
                            'end_time': segment['end_time'],
                            'text': current_text.strip(),
                            'original_text': current_text.strip()
                        })
                else:
                    # Single word, can't split
                    split_segments.append(segment)
        
        return split_segments