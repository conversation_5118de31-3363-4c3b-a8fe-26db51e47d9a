"""YouTube service for downloading videos and extracting subtitles."""

import yt_dlp
from typing import Dict, List, Any
import re
from app.core.config import settings
from app.utils.retry import youtube_retry, RetryError


class YouTubeService:
    """Service for handling YouTube video operations."""

    @staticmethod
    def is_valid_youtube_url(url: str) -> bool:
        """Validuje YouTube URL"""
        youtube_patterns = [
            r'youtube\.com/watch\?v=',
            r'youtu\.be/',
            r'youtube\.com/embed/',
            r'youtube\.com/v/'
        ]
        return any(re.search(pattern, url) for pattern in youtube_patterns)

    def get_video_info(self, url: str) -> Dict[str, Any]:
        """Získá informace o videu"""
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            return {
                "title": info.get("title"),
                "duration": info.get("duration"),
                "description": info.get("description"),
                "uploader": info.get("uploader")
            }

    def extract_subtitles(self, url: str, language: str = 'cs') -> List[Dict[str, Any]]:
        """Extrahuje titulky z videa v zadaném jazyce"""
        import tempfile
        import os
        import re

        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                print(f"Debug: Stahuji titulky pro jazyk: {language}")
                print(f"Debug: Dočasný adresář: {temp_dir}")

                ydl_opts = {
                    'writesubtitles': True,
                    'writeautomaticsub': True,
                    'subtitleslangs': [language],
                    'subtitlesformat': 'vtt',
                    'skip_download': True,
                    'quiet': False,  # Zapni logy pro debug
                    'no_warnings': False,
                    'outtmpl': os.path.join(temp_dir, '%(title)s.%(ext)s'),
                }

                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    print(f"Debug: Spouštím yt-dlp download...")
                    info = ydl.extract_info(url, download=True)

                    print(f"Debug: Obsah dočasného adresáře:")
                    for file in os.listdir(temp_dir):
                        print(f"  - {file}")

                    # Najdi stažené titulky
                    subtitle_files = []
                    for file in os.listdir(temp_dir):
                        if file.endswith(('.vtt', '.srt')):
                            full_path = os.path.join(temp_dir, file)
                            subtitle_files.append(full_path)
                            print(f"Debug: Nalezen soubor titulků: {file}")

                    if not subtitle_files:
                        print(f"Debug: Žádné soubory titulků nebyly nalezeny pro jazyk {language}")
                        return []

                    # Parsuj první nalezený soubor titulků
                    subtitle_file = subtitle_files[0]
                    print(f"Debug: Parsování souboru: {subtitle_file}")

                    result = self._parse_subtitle_file(subtitle_file)
                    print(f"Debug: Naparsováno {len(result)} segmentů")
                    return result

        except Exception as e:
            print(f"Chyba při extrakci titulků: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _parse_subtitle_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Parsuje soubor titulků do strukturovaného formátu"""
        import re

        segments = []

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        print(f"Debug: Obsah souboru titulků (prvních 500 znaků):")
        print(content[:500])
        print("Debug: ---")

        if file_path.endswith('.vtt'):
            # WebVTT formát - upravený pattern
            # Nejdříve zkusíme jednodušší pattern
            lines = content.split('\n')
            current_segment = {}
            text_lines = []

            for line in lines:
                line = line.strip()

                # Přeskoč prázdné řádky a hlavičku
                if not line or line.startswith('WEBVTT') or line.startswith('Kind:') or line.startswith('Language:'):
                    continue

                # Časový řádek
                if '-->' in line:
                    # Uložíme předchozí segment pokud existuje
                    if current_segment and text_lines:
                        current_segment['text'] = ' '.join(text_lines).strip()
                        if current_segment['text']:
                            segments.append(current_segment)

                    # Parsuj čas - odstraň dodatečné informace o pozici
                    time_parts = line.split(' --> ')
                    if len(time_parts) == 2:
                        start_time = time_parts[0].strip()
                        end_time_raw = time_parts[1].strip()

                        # Odstraň dodatečné informace (position, line, atd.)
                        end_time = end_time_raw.split(' ')[0]

                        current_segment = {
                            "start_time_ms": self._time_to_ms(start_time),
                            "end_time_ms": self._time_to_ms(end_time),
                            "sequence": len(segments) + 1
                        }
                        text_lines = []

                # Text řádek
                elif current_segment:
                    # Odstraň HTML tagy
                    clean_line = re.sub(r'<[^>]+>', '', line).strip()
                    if clean_line:
                        text_lines.append(clean_line)

            # Přidej poslední segment
            if current_segment and text_lines:
                current_segment['text'] = ' '.join(text_lines).strip()
                if current_segment['text']:
                    segments.append(current_segment)

        print(f"Debug: Naparsováno {len(segments)} segmentů")
        for i, seg in enumerate(segments[:3]):  # Zobraz první 3 segmenty
            print(f"Debug: Segment {i+1}: {seg}")

        return segments

    def _time_to_ms(self, time_str: str) -> int:
        """Převede čas ve formátu HH:MM:SS.mmm na milisekundy"""
        try:
            parts = time_str.split(':')
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds_parts = parts[2].split('.')
            seconds = int(seconds_parts[0])

            # Oprava: milisekundy mohou být 1-3 číslice, normalizuj na 3 číslice
            ms_str = seconds_parts[1]
            if len(ms_str) == 1:
                milliseconds = int(ms_str) * 100  # 5 -> 500
            elif len(ms_str) == 2:
                milliseconds = int(ms_str) * 10   # 56 -> 560
            else:
                milliseconds = int(ms_str[:3])    # 5600 -> 560, 560 -> 560

            total_ms = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds
            return total_ms

        except Exception as e:
            print(f"Chyba při parsování času '{time_str}': {e}")
            return 0

    def download_audio(self, url: str, output_dir: str, project_id: str, progress_callback=None) -> str:
        """Stáhne audio z YouTube videa s progress tracking"""
        import os
        from pathlib import Path

        try:
            # Vytvoř výstupní adresář pokud neexistuje
            Path(output_dir).mkdir(parents=True, exist_ok=True)

            # Definuj výstupní cestu
            output_template = os.path.join(output_dir, f"{project_id}_%(title)s.%(ext)s")

            def progress_hook(d):
                if progress_callback and d['status'] == 'downloading':
                    if 'total_bytes' in d:
                        percent = (d['downloaded_bytes'] / d['total_bytes']) * 100
                        progress_callback(f"Stahování audio: {percent:.1f}%")
                elif progress_callback and d['status'] == 'finished':
                    progress_callback("Audio staženo, zpracovává se...")

            ydl_opts = {
                'format': 'bestaudio/best',
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
                'outtmpl': output_template,
                'quiet': True,
                'no_warnings': True,
                'progress_hooks': [progress_hook] if progress_callback else [],
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Nejdříve získej info o videu
                info = ydl.extract_info(url, download=False)
                video_title = info.get('title', 'unknown')
                duration = info.get('duration', 0)

                # Zkontroluj délku videa (max 3 hodiny podle PRD)
                if duration > 10800:  # 3 hodiny
                    raise ValueError(f"Video je příliš dlouhé ({duration}s). Maximum je 3 hodiny.")

                # Stáhni audio
                ydl.download([url])

                # Najdi stažený soubor
                expected_filename = f"{project_id}_{video_title}.mp3"
                # Očisti název souboru od problematických znaků
                import re
                safe_filename = re.sub(r'[<>:"/\\|?*]', '_', expected_filename)
                audio_file_path = os.path.join(output_dir, safe_filename)

                # Najdi skutečný soubor (yt-dlp může změnit název)
                for file in os.listdir(output_dir):
                    if file.startswith(project_id) and file.endswith('.mp3'):
                        actual_path = os.path.join(output_dir, file)
                        if actual_path != audio_file_path:
                            os.rename(actual_path, audio_file_path)
                        break
                else:
                    raise FileNotFoundError("Stažený audio soubor nebyl nalezen")

                if progress_callback:
                    progress_callback("Audio úspěšně staženo")

                return audio_file_path

        except yt_dlp.DownloadError as e:
            raise ValueError(f"Chyba při stahování z YouTube: {str(e)}")
        except Exception as e:
            raise ValueError(f"Neočekávaná chyba při stahování audio: {str(e)}")

    def cleanup_temp_files(self, file_paths: List[str]) -> None:
        """Vyčistí dočasné soubory"""
        import os
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                # Log warning ale nepřerušuj proces
                print(f"Warning: Nepodařilo se smazat dočasný soubor {file_path}: {e}")

    def get_video_duration(self, url: str) -> int:
        """Získá délku videa v sekundách"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                return info.get('duration', 0)
        except Exception:
            return 0

    def get_available_subtitle_languages(self, url: str) -> List[dict]:
        """Získá seznam skutečně dostupných jazyků titulků"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'ignoreerrors': True,  # Pokračuj i při chybách
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                try:
                    info = ydl.extract_info(url, download=False)
                except Exception as e:
                    print(f"Chyba při načítání video info: {e}")
                    # Zkus základní informace
                    if "private" in str(e).lower() or "unavailable" in str(e).lower():
                        raise Exception("Video není dostupné (soukromé nebo smazané)")
                    raise e

                available_languages = []

                print(f"Debug: Dostupné manuální titulky: {list(info.get('subtitles', {}).keys()) if 'subtitles' in info else 'Žádné'}")
                print(f"Debug: Dostupné automatické titulky: {list(info.get('automatic_captions', {}).keys()) if 'automatic_captions' in info else 'Žádné'}")

                # Manuální titulky (priorita)
                if 'subtitles' in info and info['subtitles']:
                    for lang_code, subtitle_list in info['subtitles'].items():
                        if subtitle_list and len(subtitle_list) > 0:
                            # Ověř, že titulky skutečně existují
                            try:
                                # Zkus najít VTT formát
                                vtt_found = any(sub.get('ext') == 'vtt' for sub in subtitle_list)
                                if vtt_found:
                                    available_languages.append({
                                        'code': lang_code,
                                        'name': self._get_language_name(lang_code),
                                        'type': 'manuální'
                                    })
                                    print(f"Debug: Přidán manuální jazyk: {lang_code}")
                            except Exception as e:
                                print(f"Debug: Chyba při ověřování manuálních titulků {lang_code}: {e}")

                # Automatické titulky (pouze pokud není manuální verze)
                if 'automatic_captions' in info and info['automatic_captions']:
                    for lang_code, subtitle_list in info['automatic_captions'].items():
                        if subtitle_list and len(subtitle_list) > 0:
                            # Přidej pouze pokud už není manuální verze
                            if not any(lang['code'] == lang_code for lang in available_languages):
                                try:
                                    # Zkus najít VTT formát
                                    vtt_found = any(sub.get('ext') == 'vtt' for sub in subtitle_list)
                                    if vtt_found:
                                        available_languages.append({
                                            'code': lang_code,
                                            'name': self._get_language_name(lang_code),
                                            'type': 'automatické'
                                        })
                                        print(f"Debug: Přidán automatický jazyk: {lang_code}")
                                except Exception as e:
                                    print(f"Debug: Chyba při ověřování automatických titulků {lang_code}: {e}")

                print(f"Debug: Celkem nalezeno jazyků: {len(available_languages)}")
                return available_languages

        except Exception as e:
            print(f"Chyba při získávání jazyků titulků: {e}")
            if "private" in str(e).lower() or "unavailable" in str(e).lower():
                raise Exception("Video není dostupné (soukromé nebo smazané)")
            return []

    def _get_language_name(self, lang_code: str) -> str:
        """Převede kód jazyka na název"""
        language_names = {
            'cs': 'Čeština',
            'sk': 'Slovenčina',
            'en': 'English',
            'de': 'Deutsch',
            'fr': 'Français',
            'es': 'Español',
            'it': 'Italiano',
            'pl': 'Polski',
            'ru': 'Русский',
            'uk': 'Українська'
        }
        return language_names.get(lang_code, lang_code.upper())
