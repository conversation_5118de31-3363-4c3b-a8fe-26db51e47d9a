"""Main processing service for subtitle correction workflow."""

import asyncio
import logging
import uuid
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

from sqlalchemy.orm import Session
from fastapi import HTTPException

from app.core.config import settings
from app.core.database import get_db
from app.models.project import Project
# from app.schemas.project import ProjectStatus  # Not needed for MVP
from app.models.subtitle_segment import SubtitleSegment
from app.models.correction_suggestion import CorrectionSuggestion
from app.models.user_dictionary import UserDictionaryTerm
from app.services.youtube_service import YouTubeService
from app.services.openai_service import OpenAIService
from app.services.subtitle_parser import SubtitleParser
from app.schemas.project import ProjectCreate, ProjectUpdate
from app.schemas.subtitle_segment import SubtitleSegmentCreate, SubtitleSegmentUpdate

logger = logging.getLogger(__name__)


class ProcessingService:
    """Main service for processing YouTube videos and correcting subtitles."""
    
    def __init__(self):
        self.youtube_service = YouTubeService()
        self.openai_service = OpenAIService()
        self.subtitle_parser = SubtitleParser()
    
    async def create_project(
        self,
        youtube_url: str,
        db: Session
    ) -> Project:
        """Create a new project for subtitle correction."""
        try:
            # Validate YouTube URL
            if not self.youtube_service.validate_url(youtube_url):
                raise HTTPException(
                    status_code=400,
                    detail="Invalid YouTube URL"
                )
            
            # Extract video info
            video_info = self.youtube_service.extract_video_info(youtube_url)
            
            # Create project
            project = Project(
                youtube_url=youtube_url,
                title=video_info.get('title', 'Unknown'),
                # duration=video_info.get('duration', 0),  # temporarily disabled
                status="pending",
                youtube_video_id=self.youtube_service.get_video_id(youtube_url) or "unknown"
            )
            
            db.add(project)
            db.commit()
            db.refresh(project)
            
            logger.info(f"Created project {project.id} for {youtube_url}")
            return project
            
        except Exception as e:
            logger.error(f"Failed to create project: {str(e)}")
            raise
    
    async def process_project(
        self,
        project_id: int,
        db: Session
    ) -> Project:
        """Process a project: download, transcribe, and correct subtitles."""
        try:
            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise HTTPException(
                    status_code=404,
                    detail="Project not found"
                )
            
            # Update status
            project.status = "processing"
            project.updated_at = datetime.utcnow()
            db.commit()
            
            # Step 1: Download audio
            audio_path = await self._download_audio(project.youtube_url, project.id)
            
            # Step 2: Get original subtitles if available
            original_subtitles = await self._get_original_subtitles(project.youtube_url)
            
            # Step 3: Transcribe audio
            transcription = await self._transcribe_audio(audio_path)
            
            # Step 4: Parse and align subtitles
            segments = await self._parse_and_align_subtitles(
                original_subtitles,
                transcription,
                project.duration
            )
            
            # Step 5: Apply corrections
            corrected_segments = await self._apply_corrections(
                segments,
                project_id,
                db
            )
            
            # Step 6: Save segments to database
            await self._save_segments(project_id, corrected_segments, db)
            
            # Update project status
            project.status = "needs_review"
            project.updated_at = datetime.utcnow()
            db.commit()
            
            logger.info(f"Completed processing project {project_id}")
            return project
            
        except Exception as e:
            logger.error(f"Failed to process project {project_id}: {str(e)}")
            
            # Update project status to error
            project = db.query(Project).filter(Project.id == project_id).first()
            if project:
                project.status = "error"
                project.error_message = str(e)
                project.updated_at = datetime.utcnow()
                db.commit()
            
            raise
    
    async def _download_audio(
        self,
        youtube_url: str,
        project_id: int
    ) -> str:
        """Download audio from YouTube video."""
        try:
            output_path = str(Path(settings.TEMP_DIR) / f"{project_id}_audio")
            audio_path = self.youtube_service.extract_audio(
                youtube_url,
                output_path=output_path,
                audio_format='wav',
                audio_quality='192'
            )
            
            logger.info(f"Downloaded audio for project {project_id}: {audio_path}")
            return audio_path
            
        except Exception as e:
            logger.error(f"Failed to download audio: {str(e)}")
            raise
    
    async def _get_original_subtitles(
        self,
        youtube_url: str
    ) -> Optional[List[Dict[str, Any]]]:
        """Get original subtitles from YouTube if available."""
        try:
            subtitle_file = self.youtube_service.get_subtitles(
                youtube_url,
                language='cs',
                auto_subtitles=True
            )
            
            if subtitle_file:
                segments = self.subtitle_parser.parse_file(subtitle_file, 'srt')
                logger.info(f"Found original subtitles with {len(segments)} segments")
                return segments
            
            logger.info("No original subtitles found")
            return None
            
        except Exception as e:
            logger.warning(f"Failed to get original subtitles: {str(e)}")
            return None
    
    async def _transcribe_audio(
        self,
        audio_path: str
    ) -> List[Dict[str, Any]]:
        """Transcribe audio using OpenAI Whisper."""
        try:
            transcription = await self.openai_service.transcribe_audio(
                audio_path,
                language='cs'
            )
            
            logger.info(f"Transcribed audio into {len(transcription)} segments")
            return transcription
            
        except Exception as e:
            logger.error(f"Failed to transcribe audio: {str(e)}")
            raise
    
    async def _parse_and_align_subtitles(
        self,
        original_subtitles: Optional[List[Dict[str, Any]]],
        transcription: List[Dict[str, Any]],
        video_duration: int
    ) -> List[Dict[str, Any]]:
        """Parse and align subtitles with transcription."""
        try:
            if original_subtitles:
                # Use original subtitles as base and align with transcription
                segments = self._align_subtitles_with_transcription(
                    original_subtitles,
                    transcription
                )
            else:
                # Use transcription as base
                segments = transcription
            
            # Validate and clean segments
            segments = self._validate_segments(segments, video_duration)
            
            logger.info(f"Parsed and aligned {len(segments)} segments")
            return segments
            
        except Exception as e:
            logger.error(f"Failed to parse and align subtitles: {str(e)}")
            raise
    
    def _align_subtitles_with_transcription(
        self,
        subtitles: List[Dict[str, Any]],
        transcription: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Align original subtitles with transcription for better accuracy."""
        # Simple alignment - in production, use more sophisticated algorithms
        aligned_segments = []
        
        for subtitle in subtitles:
            # Find best matching transcription segment
            best_match = None
            best_score = 0
            
            for trans in transcription:
                # Simple overlap check
                overlap_start = max(subtitle['start_time'], trans['start_time'])
                overlap_end = min(subtitle['end_time'], trans['end_time'])
                
                if overlap_end > overlap_start:
                    # Calculate overlap ratio
                    overlap_duration = overlap_end - overlap_start
                    subtitle_duration = subtitle['end_time'] - subtitle['start_time']
                    score = overlap_duration / subtitle_duration
                    
                    if score > best_score:
                        best_score = score
                        best_match = trans
            
            if best_match and best_score > 0.5:
                # Use transcription text with original timing
                aligned_segments.append({
                    'start_time': subtitle['start_time'],
                    'end_time': subtitle['end_time'],
                    'text': best_match['text'],
                    'original_text': subtitle['text']
                })
            else:
                # Keep original subtitle
                aligned_segments.append(subtitle)
        
        return aligned_segments
    
    def _validate_segments(
        self,
        segments: List[Dict[str, Any]],
        video_duration: int
    ) -> List[Dict[str, Any]]:
        """Validate and clean subtitle segments."""
        validated_segments = []
        
        for i, segment in enumerate(segments):
            # Ensure valid timing
            start_time = max(0, segment['start_time'])
            end_time = min(video_duration, segment['end_time'])
            
            if end_time <= start_time:
                continue
            
            # Ensure valid text
            text = segment.get('text', '').strip()
            if not text:
                continue
            
            validated_segments.append({
                'start_time': start_time,
                'end_time': end_time,
                'text': text,
                'original_text': segment.get('original_text', text)
            })
        
        return validated_segments
    
    async def _apply_corrections(
        self,
        segments: List[Dict[str, Any]],
        project_id: int,
        db: Session
    ) -> List[Dict[str, Any]]:
        """Apply AI corrections to subtitle segments."""
        try:
            # Get custom dictionary
            dictionary_terms = db.query(UserDictionary).all()
            custom_dictionary = [term.phrase for term in dictionary_terms]
            
            # Prepare segments for correction
            segment_dicts = [
                {
                    'start_time': seg['start_time'],
                    'end_time': seg['end_time'],
                    'text': seg['text'],
                    'original_text': seg['original_text']
                }
                for seg in segments
            ]
            
            # Apply corrections
            corrected_segments = await self.openai_service.correct_subtitle_segments(
                segment_dicts,
                language='cs',
                custom_dictionary=custom_dictionary
            )
            
            logger.info(f"Applied corrections to {len(corrected_segments)} segments")
            return corrected_segments
            
        except Exception as e:
            logger.error(f"Failed to apply corrections: {str(e)}")
            raise
    
    async def _save_segments(
        self,
        project_id: int,
        segments: List[Dict[str, Any]],
        db: Session
    ) -> None:
        """Save corrected segments to database."""
        try:
            # Clear existing segments
            db.query(SubtitleSegment).filter(
                SubtitleSegment.project_id == project_id
            ).delete()
            
            # Save new segments
            for i, segment in enumerate(segments):
                subtitle_segment = SubtitleSegment(
                    project_id=project_id,
                    segment_index=i + 1,
                    start_time=segment['start_time'],
                    end_time=segment['end_time'],
                    original_text=segment['original_text'],
                    corrected_text=segment.get('corrected_text', segment['text']),
                    confidence_score=segment.get('confidence', 0.0),
                    is_processed=True
                )
                
                db.add(subtitle_segment)
                
                # Save correction suggestions
                if segment.get('changes'):
                    for change in segment['changes']:
                        suggestion = CorrectionSuggestion(
                            subtitle_segment_id=subtitle_segment.id,
                            original_text=change.get('original', ''),
                            suggested_text=change.get('corrected', ''),
                            confidence_score=change.get('confidence', 0.0),
                            reason=change.get('explanation', ''),
                            suggestion_type='ai_correction'
                        )
                        db.add(suggestion)
            
            db.commit()
            logger.info(f"Saved {len(segments)} segments for project {project_id}")
            
        except Exception as e:
            logger.error(f"Failed to save segments: {str(e)}")
            raise
    
    async def update_segment(
        self,
        segment_id: int,
        corrected_text: str,
        db: Session
    ) -> SubtitleSegment:
        """Update a subtitle segment with user corrections."""
        try:
            segment = db.query(SubtitleSegment).filter(
                SubtitleSegment.id == segment_id
            ).first()
            
            if not segment:
                raise HTTPException(
                    status_code=404,
                    detail="Segment not found"
                )
            
            segment.corrected_text = corrected_text
            segment.is_processed = True
            segment.updated_at = datetime.utcnow()
            
            db.commit()
            db.refresh(segment)
            
            logger.info(f"Updated segment {segment_id}")
            return segment
            
        except Exception as e:
            logger.error(f"Failed to update segment: {str(e)}")
            raise
    
    async def export_subtitles(
        self,
        project_id: int,
        db: Session,
        format_type: str = 'srt'
    ) -> str:
        """Export corrected subtitles in specified format."""
        try:
            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise HTTPException(
                    status_code=404,
                    detail="Project not found"
                )
            
            segments = db.query(SubtitleSegment).filter(
                SubtitleSegment.project_id == project_id
            ).order_by(SubtitleSegment.segment_index).all()
            
            if not segments:
                raise HTTPException(
                    status_code=404,
                    detail="No segments found for project"
                )
            
            # Convert to dict format
            segment_dicts = [
                {
                    'start_time': seg.start_time,
                    'end_time': seg.end_time,
                    'text': seg.corrected_text,
                    'original_text': seg.original_text
                }
                for seg in segments
            ]
            
            # Generate subtitle file
            subtitle_content = self.subtitle_parser.generate_file(
                segment_dicts,
                format_type
            )
            
            logger.info(f"Exported subtitles for project {project_id} in {format_type} format")
            return subtitle_content
            
        except Exception as e:
            logger.error(f"Failed to export subtitles: {str(e)}")
            raise
    
    async def get_project_statistics(
        self,
        project_id: int,
        db: Session
    ) -> Dict[str, Any]:
        """Get processing statistics for a project."""
        try:
            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise HTTPException(
                    status_code=404,
                    detail="Project not found"
                )
            
            segments = db.query(SubtitleSegment).filter(
                SubtitleSegment.project_id == project_id
            ).all()
            
            total_segments = len(segments)
            processed = len([s for s in segments if s.is_processed])
            unprocessed = total_segments - processed

            return {
                'total_segments': total_segments,
                'needs_review': unprocessed,
                'auto_corrected': processed,
                'user_modified': 0,  # For MVP
                'unchanged': 0,  # For MVP
                'completion_percentage': (
                    processed / total_segments * 100
                    if total_segments > 0 else 0
                )
            }
            
        except Exception as e:
            logger.error(f"Failed to get project statistics: {str(e)}")
            raise
    
    async def cleanup_project_files(
        self,
        project_id: int
    ) -> None:
        """Clean up temporary files for a project."""
        try:
            temp_dir = Path(settings.TEMP_DIR)
            
            # Remove audio files
            audio_files = temp_dir.glob(f"{project_id}_audio*")
            for file_path in audio_files:
                if file_path.exists():
                    file_path.unlink()
            
            # Remove subtitle files
            subtitle_files = temp_dir.glob(f"{project_id}_subtitles*")
            for file_path in subtitle_files:
                if file_path.exists():
                    file_path.unlink()
            
            logger.info(f"Cleaned up temporary files for project {project_id}")
            
        except Exception as e:
            logger.error(f"Failed to cleanup project files: {str(e)}")
            # Don't raise error for cleanup failures