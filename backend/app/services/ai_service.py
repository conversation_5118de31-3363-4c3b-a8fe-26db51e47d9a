"""AI service for subtitle correction using OpenAI."""

from openai import OpenAI
from typing import List, Dict, Any
from app.core.config import settings


class AIService:
    """Service for AI-powered subtitle correction."""
    
    def __init__(self):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
    
    def process_subtitles(self, subtitles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Zpracuje titulky pomocí AI pro korekce"""
        corrected_segments = []
        
        for segment in subtitles:
            original_text = segment["text"]
            
            # Pošli text na korekci
            corrected_text = self.correct_text(original_text)
            
            corrected_segments.append({
                "start_time_ms": segment["start_time_ms"],
                "end_time_ms": segment["end_time_ms"],
                "original_text": original_text,
                "corrected_text": corrected_text,
                "has_corrections": original_text != corrected_text
            })
        
        return corrected_segments
    
    def correct_text(self, text: str) -> str:
        """Opraví text pomocí OpenAI"""
        try:
            response = self.client.chat.completions.create(
                model=settings.GPT_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": """Jsi expert na korekci českých titulků. Oprav gramatické chyby, 
                        překlepy a zlepši čitelnost textu. Zachovej původní význam a styl. 
                        Vrať pouze opravený text bez dalších komentářů."""
                    },
                    {
                        "role": "user",
                        "content": f"Oprav tento text: {text}"
                    }
                ],
                temperature=0.1,
                max_tokens=500
            )
            
            return response.choices[0].message.content.strip()
        except Exception as e:
            # V případě chyby vrať původní text
            return text
    
    def generate_suggestions(self, text: str) -> List[Dict[str, Any]]:
        """Generuje návrhy na zlepšení textu"""
        try:
            response = self.client.chat.completions.create(
                model=settings.GPT_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": """Analyzuj text a navrhni konkrétní zlepšení. 
                        Vrať JSON seznam návrhů s poli: type, confidence, description, 
                        original_fragment, suggested_fragment."""
                    },
                    {
                        "role": "user",
                        "content": text
                    }
                ],
                temperature=0.2,
                max_tokens=1000
            )
            
            # Parsuj JSON odpověď
            import json
            suggestions = json.loads(response.choices[0].message.content)
            return suggestions
        except Exception:
            return []
