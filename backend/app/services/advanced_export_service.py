"""Advanced export service with multiple formats and options."""

import logging
from typing import List, Dict, Any, Optional, Set
from pathlib import Path
import json
from datetime import timedelta
from sqlalchemy.orm import Session

from app.models.subtitle_segment import SubtitleSegment
from app.models.project import Project
from app.utils.monitoring import monitor_performance

logger = logging.getLogger(__name__)


class AdvancedExportService:
    """Advanced export service with multiple formats and validation."""
    
    SUPPORTED_FORMATS = {'srt', 'vtt', 'txt', 'json', 'csv'}
    SUPPORTED_ENCODINGS = {'utf-8', 'utf-16', 'ascii', 'iso-8859-1'}
    
    def __init__(self):
        self.logger = logger
    
    @monitor_performance("advanced_export", include_system_metrics=True)
    async def export_subtitles(
        self,
        project_id: str,
        db: Session,
        format_type: str = 'srt',
        encoding: str = 'utf-8',
        include_original: bool = False,
        include_timestamps: bool = True,
        selected_segments: Optional[List[str]] = None,
        custom_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Export subtitles with advanced options.
        
        Args:
            project_id: Project identifier
            db: Database session
            format_type: Export format (srt, vtt, txt, json, csv)
            encoding: Text encoding
            include_original: Include original text alongside corrected
            include_timestamps: Include timing information
            selected_segments: List of segment IDs to export (None = all)
            custom_options: Additional format-specific options
            
        Returns:
            Export result with content, metadata, and validation
        """
        try:
            # Validate inputs
            self._validate_export_options(format_type, encoding, custom_options)
            
            # Get project and segments
            project = db.query(Project).filter(Project.project_id == project_id).first()
            if not project:
                raise ValueError(f"Project {project_id} not found")
            
            segments_query = db.query(SubtitleSegment).filter(
                SubtitleSegment.project_id == project_id
            )
            
            # Filter selected segments if specified
            if selected_segments:
                segments_query = segments_query.filter(
                    SubtitleSegment.segment_id.in_(selected_segments)
                )
            
            segments = segments_query.order_by(SubtitleSegment.sequence_number).all()
            
            if not segments:
                raise ValueError("No segments found for export")
            
            # Generate content based on format
            content = await self._generate_content(
                segments,
                format_type,
                include_original,
                include_timestamps,
                custom_options or {}
            )
            
            # Validate content
            validation_result = await self._validate_content(content, format_type)
            
            # Generate filename
            filename = self._generate_filename(
                project.video_title or "subtitles",
                format_type,
                selected_segments is not None
            )
            
            # Prepare result
            result = {
                "content": content,
                "filename": filename,
                "format": format_type,
                "encoding": encoding,
                "metadata": {
                    "project_id": project_id,
                    "project_title": project.video_title,
                    "segments_count": len(segments),
                    "total_duration": self._calculate_total_duration(segments),
                    "export_options": {
                        "include_original": include_original,
                        "include_timestamps": include_timestamps,
                        "selected_segments_only": selected_segments is not None,
                        "custom_options": custom_options
                    }
                },
                "validation": validation_result,
                "size_bytes": len(content.encode(encoding))
            }
            
            self.logger.info(
                f"Successfully exported {len(segments)} segments "
                f"in {format_type} format for project {project_id}"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Export failed: {str(e)}")
            raise
    
    def _validate_export_options(
        self,
        format_type: str,
        encoding: str,
        custom_options: Optional[Dict[str, Any]]
    ):
        """Validate export options."""
        if format_type not in self.SUPPORTED_FORMATS:
            raise ValueError(
                f"Unsupported format: {format_type}. "
                f"Supported: {', '.join(self.SUPPORTED_FORMATS)}"
            )
        
        if encoding not in self.SUPPORTED_ENCODINGS:
            raise ValueError(
                f"Unsupported encoding: {encoding}. "
                f"Supported: {', '.join(self.SUPPORTED_ENCODINGS)}"
            )
    
    async def _generate_content(
        self,
        segments: List[SubtitleSegment],
        format_type: str,
        include_original: bool,
        include_timestamps: bool,
        custom_options: Dict[str, Any]
    ) -> str:
        """Generate content based on format type."""
        if format_type == 'srt':
            return self._generate_srt(segments, include_original, custom_options)
        elif format_type == 'vtt':
            return self._generate_vtt(segments, include_original, custom_options)
        elif format_type == 'txt':
            return self._generate_txt(segments, include_original, include_timestamps)
        elif format_type == 'json':
            return self._generate_json(segments, include_original, custom_options)
        elif format_type == 'csv':
            return self._generate_csv(segments, include_original, include_timestamps)
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def _generate_srt(
        self,
        segments: List[SubtitleSegment],
        include_original: bool,
        custom_options: Dict[str, Any]
    ) -> str:
        """Generate SRT format content."""
        lines = []
        
        for i, segment in enumerate(segments, 1):
            # Sequence number
            lines.append(str(i))
            
            # Timing
            start_time = self._format_srt_time(segment.start_time_ms / 1000)
            end_time = self._format_srt_time(segment.end_time_ms / 1000)
            lines.append(f"{start_time} --> {end_time}")
            
            # Text content
            text = segment.corrected_text or segment.original_text
            if include_original and segment.original_text != segment.corrected_text:
                lines.append(f"Original: {segment.original_text}")
                lines.append(f"Corrected: {text}")
            else:
                lines.append(text)
            
            # Empty line between segments
            lines.append("")
        
        return "\n".join(lines).rstrip()
    
    def _generate_vtt(
        self,
        segments: List[SubtitleSegment],
        include_original: bool,
        custom_options: Dict[str, Any]
    ) -> str:
        """Generate WebVTT format content."""
        lines = ["WEBVTT", ""]
        
        for segment in segments:
            # Timing
            start_time = self._format_vtt_time(segment.start_time_ms / 1000)
            end_time = self._format_vtt_time(segment.end_time_ms / 1000)
            lines.append(f"{start_time} --> {end_time}")
            
            # Text content
            text = segment.corrected_text or segment.original_text
            if include_original and segment.original_text != segment.corrected_text:
                lines.append(f"{segment.original_text} → {text}")
            else:
                lines.append(text)
            
            lines.append("")
        
        return "\n".join(lines).rstrip()
    
    def _generate_txt(
        self,
        segments: List[SubtitleSegment],
        include_original: bool,
        include_timestamps: bool
    ) -> str:
        """Generate plain text format."""
        lines = []
        
        for segment in segments:
            text = segment.corrected_text or segment.original_text
            
            if include_timestamps:
                start_time = self._format_readable_time(segment.start_time_ms / 1000)
                line = f"[{start_time}] {text}"
            else:
                line = text
            
            if include_original and segment.original_text != segment.corrected_text:
                lines.append(f"Original: {segment.original_text}")
                lines.append(f"Corrected: {line}")
            else:
                lines.append(line)
        
        return "\n".join(lines)
    
    def _generate_json(
        self,
        segments: List[SubtitleSegment],
        include_original: bool,
        custom_options: Dict[str, Any]
    ) -> str:
        """Generate JSON format content."""
        data = {
            "format": "subtitle_json",
            "version": "1.0",
            "segments": []
        }
        
        for segment in segments:
            segment_data = {
                "id": segment.segment_id,
                "sequence": segment.sequence_number,
                "start_time_ms": segment.start_time_ms,
                "end_time_ms": segment.end_time_ms,
                "duration_ms": segment.end_time_ms - segment.start_time_ms,
                "text": segment.corrected_text or segment.original_text
            }
            
            if include_original:
                segment_data["original_text"] = segment.original_text
                segment_data["corrected_text"] = segment.corrected_text
            
            if hasattr(segment, 'suggestions') and segment.suggestions:
                segment_data["suggestions"] = [
                    {
                        "id": s.suggestion_id,
                        "type": s.type,
                        "confidence": s.confidence,
                        "original_fragment": s.original_fragment,
                        "suggested_fragment": s.suggested_fragment,
                        "applied": s.applied
                    }
                    for s in segment.suggestions
                ]
            
            data["segments"].append(segment_data)
        
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    def _generate_csv(
        self,
        segments: List[SubtitleSegment],
        include_original: bool,
        include_timestamps: bool
    ) -> str:
        """Generate CSV format content."""
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Header
        headers = ["sequence", "start_time", "end_time", "duration_sec", "text"]
        if include_original:
            headers.extend(["original_text", "corrected_text"])
        
        writer.writerow(headers)
        
        # Data rows
        for segment in segments:
            row = [
                segment.sequence_number,
                self._format_readable_time(segment.start_time_ms / 1000),
                self._format_readable_time(segment.end_time_ms / 1000),
                round((segment.end_time_ms - segment.start_time_ms) / 1000, 2),
                segment.corrected_text or segment.original_text
            ]
            
            if include_original:
                row.extend([segment.original_text, segment.corrected_text])
            
            writer.writerow(row)
        
        return output.getvalue()
    
    async def _validate_content(self, content: str, format_type: str) -> Dict[str, Any]:
        """Validate exported content."""
        validation = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "stats": {
                "size_bytes": len(content.encode('utf-8')),
                "lines_count": len(content.split('\n')),
                "characters_count": len(content)
            }
        }
        
        # Format-specific validation
        if format_type == 'srt':
            validation.update(self._validate_srt_content(content))
        elif format_type == 'vtt':
            validation.update(self._validate_vtt_content(content))
        elif format_type == 'json':
            validation.update(self._validate_json_content(content))
        
        return validation
    
    def _validate_srt_content(self, content: str) -> Dict[str, Any]:
        """Validate SRT content."""
        errors = []
        warnings = []
        
        lines = content.split('\n')
        segment_count = 0
        
        i = 0
        while i < len(lines):
            if lines[i].strip().isdigit():
                segment_count += 1
                # Check timing line
                if i + 1 < len(lines) and '-->' not in lines[i + 1]:
                    errors.append(f"Missing timing in segment {segment_count}")
                i += 2
            else:
                i += 1
        
        return {
            "format_errors": errors,
            "format_warnings": warnings,
            "segments_detected": segment_count
        }
    
    def _validate_vtt_content(self, content: str) -> Dict[str, Any]:
        """Validate WebVTT content."""
        errors = []
        warnings = []
        
        if not content.startswith('WEBVTT'):
            errors.append("Missing WEBVTT header")
        
        return {
            "format_errors": errors,
            "format_warnings": warnings
        }
    
    def _validate_json_content(self, content: str) -> Dict[str, Any]:
        """Validate JSON content."""
        errors = []
        
        try:
            json.loads(content)
        except json.JSONDecodeError as e:
            errors.append(f"Invalid JSON: {str(e)}")
        
        return {
            "format_errors": errors,
            "format_warnings": []
        }
    
    def _format_srt_time(self, seconds: float) -> str:
        """Format time for SRT (HH:MM:SS,mmm)."""
        td = timedelta(seconds=seconds)
        total_seconds = int(td.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        secs = total_seconds % 60
        milliseconds = int((seconds - int(seconds)) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
    
    def _format_vtt_time(self, seconds: float) -> str:
        """Format time for WebVTT (HH:MM:SS.mmm)."""
        return self._format_srt_time(seconds).replace(',', '.')
    
    def _format_readable_time(self, seconds: float) -> str:
        """Format time in readable format."""
        return self._format_srt_time(seconds).replace(',', '.')
    
    def _calculate_total_duration(self, segments: List[SubtitleSegment]) -> float:
        """Calculate total duration of segments."""
        if not segments:
            return 0.0
        return (segments[-1].end_time_ms - segments[0].start_time_ms) / 1000
    
    def _generate_filename(
        self,
        base_name: str,
        format_type: str,
        is_partial: bool = False
    ) -> str:
        """Generate appropriate filename."""
        # Clean base name
        clean_name = "".join(c for c in base_name if c.isalnum() or c in (' ', '-', '_')).strip()
        clean_name = clean_name.replace(' ', '_')
        
        if is_partial:
            clean_name += "_partial"
        
        return f"{clean_name}.{format_type}"
