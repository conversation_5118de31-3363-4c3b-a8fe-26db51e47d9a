"""Monitoring and performance tracking utilities."""

import time
import logging
import asyncio
from typing import Dict, Any, Optional, Callable
from functools import wraps
from contextlib import asynccontextmanager
import psutil
import os

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """Monitor performance metrics for operations."""
    
    def __init__(self):
        self.metrics: Dict[str, Dict[str, Any]] = {}
    
    def record_operation(
        self,
        operation_name: str,
        duration: float,
        success: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Record metrics for an operation."""
        if operation_name not in self.metrics:
            self.metrics[operation_name] = {
                'total_calls': 0,
                'successful_calls': 0,
                'failed_calls': 0,
                'total_duration': 0.0,
                'min_duration': float('inf'),
                'max_duration': 0.0,
                'avg_duration': 0.0,
                'last_call': None,
                'metadata': []
            }
        
        metrics = self.metrics[operation_name]
        metrics['total_calls'] += 1
        
        if success:
            metrics['successful_calls'] += 1
        else:
            metrics['failed_calls'] += 1
        
        metrics['total_duration'] += duration
        metrics['min_duration'] = min(metrics['min_duration'], duration)
        metrics['max_duration'] = max(metrics['max_duration'], duration)
        metrics['avg_duration'] = metrics['total_duration'] / metrics['total_calls']
        metrics['last_call'] = time.time()
        
        if metadata:
            metrics['metadata'].append({
                'timestamp': time.time(),
                'duration': duration,
                'success': success,
                **metadata
            })
            # Keep only last 10 metadata entries
            metrics['metadata'] = metrics['metadata'][-10:]
    
    def get_metrics(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """Get metrics for specific operation or all operations."""
        if operation_name:
            return self.metrics.get(operation_name, {})
        return self.metrics
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary of all metrics."""
        summary = {
            'total_operations': len(self.metrics),
            'operations': {}
        }
        
        for op_name, metrics in self.metrics.items():
            summary['operations'][op_name] = {
                'total_calls': metrics['total_calls'],
                'success_rate': metrics['successful_calls'] / metrics['total_calls'] if metrics['total_calls'] > 0 else 0,
                'avg_duration': metrics['avg_duration'],
                'last_call': metrics['last_call']
            }
        
        return summary


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def monitor_performance(operation_name: str, include_system_metrics: bool = False):
    """Decorator to monitor function performance."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss if include_system_metrics else None
            success = True
            metadata = {}
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                metadata['error'] = str(e)
                raise
            finally:
                duration = time.time() - start_time
                
                if include_system_metrics and start_memory:
                    end_memory = psutil.Process().memory_info().rss
                    metadata['memory_delta'] = end_memory - start_memory
                    metadata['peak_memory'] = end_memory
                
                performance_monitor.record_operation(
                    operation_name,
                    duration,
                    success,
                    metadata
                )
                
                logger.info(
                    f"Operation '{operation_name}' completed in {duration:.2f}s "
                    f"(success: {success})"
                )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss if include_system_metrics else None
            success = True
            metadata = {}
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                metadata['error'] = str(e)
                raise
            finally:
                duration = time.time() - start_time
                
                if include_system_metrics and start_memory:
                    end_memory = psutil.Process().memory_info().rss
                    metadata['memory_delta'] = end_memory - start_memory
                    metadata['peak_memory'] = end_memory
                
                performance_monitor.record_operation(
                    operation_name,
                    duration,
                    success,
                    metadata
                )
                
                logger.info(
                    f"Operation '{operation_name}' completed in {duration:.2f}s "
                    f"(success: {success})"
                )
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


@asynccontextmanager
async def operation_context(operation_name: str, metadata: Optional[Dict[str, Any]] = None):
    """Context manager for monitoring operations."""
    start_time = time.time()
    success = True
    final_metadata = metadata or {}
    
    try:
        yield
    except Exception as e:
        success = False
        final_metadata['error'] = str(e)
        raise
    finally:
        duration = time.time() - start_time
        performance_monitor.record_operation(
            operation_name,
            duration,
            success,
            final_metadata
        )


def log_system_resources():
    """Log current system resource usage."""
    process = psutil.Process()
    memory_info = process.memory_info()
    cpu_percent = process.cpu_percent()
    
    logger.info(
        f"System resources - "
        f"Memory: {memory_info.rss / 1024 / 1024:.1f} MB, "
        f"CPU: {cpu_percent:.1f}%"
    )


def get_performance_report() -> Dict[str, Any]:
    """Get comprehensive performance report."""
    return {
        'timestamp': time.time(),
        'system': {
            'memory_usage_mb': psutil.Process().memory_info().rss / 1024 / 1024,
            'cpu_percent': psutil.Process().cpu_percent(),
            'disk_usage': psutil.disk_usage('/').percent if os.path.exists('/') else None
        },
        'operations': performance_monitor.get_summary()
    }
