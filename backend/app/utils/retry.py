"""Retry utilities for robust API calls and operations."""

import asyncio
import logging
from typing import Any, Callable, Optional, Type, Union
from functools import wraps

logger = logging.getLogger(__name__)


class RetryError(Exception):
    """Exception raised when all retry attempts fail."""
    
    def __init__(self, message: str, last_exception: Exception):
        super().__init__(message)
        self.last_exception = last_exception


async def async_retry(
    func: Callable,
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,),
    on_retry: Optional[Callable] = None
) -> Any:
    """
    Retry an async function with exponential backoff.
    
    Args:
        func: The async function to retry
        max_attempts: Maximum number of attempts
        delay: Initial delay between attempts in seconds
        backoff_factor: Factor to multiply delay by after each attempt
        exceptions: Tuple of exceptions to catch and retry on
        on_retry: Optional callback function called on each retry
    
    Returns:
        The result of the function call
        
    Raises:
        RetryError: If all attempts fail
    """
    last_exception = None
    current_delay = delay
    
    for attempt in range(max_attempts):
        try:
            result = await func()
            if attempt > 0:
                logger.info(f"Function succeeded on attempt {attempt + 1}")
            return result
            
        except exceptions as e:
            last_exception = e
            
            if attempt == max_attempts - 1:
                # Last attempt failed
                break
                
            logger.warning(
                f"Attempt {attempt + 1} failed: {str(e)}. "
                f"Retrying in {current_delay:.1f} seconds..."
            )
            
            if on_retry:
                await on_retry(attempt + 1, e)
                
            await asyncio.sleep(current_delay)
            current_delay *= backoff_factor
    
    # All attempts failed
    error_msg = f"All {max_attempts} attempts failed. Last error: {str(last_exception)}"
    logger.error(error_msg)
    raise RetryError(error_msg, last_exception)


def retry_decorator(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,),
    on_retry: Optional[Callable] = None
):
    """
    Decorator for adding retry logic to async functions.
    
    Usage:
        @retry_decorator(max_attempts=3, delay=1.0)
        async def my_function():
            # Function that might fail
            pass
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            async def call_func():
                return await func(*args, **kwargs)
            
            return await async_retry(
                call_func,
                max_attempts=max_attempts,
                delay=delay,
                backoff_factor=backoff_factor,
                exceptions=exceptions,
                on_retry=on_retry
            )
        return wrapper
    return decorator


# Specific retry configurations for different operations
def openai_retry(max_attempts: int = 3):
    """Retry decorator specifically for OpenAI API calls."""
    import openai
    
    return retry_decorator(
        max_attempts=max_attempts,
        delay=1.0,
        backoff_factor=2.0,
        exceptions=(
            openai.RateLimitError,
            openai.APITimeoutError,
            openai.APIConnectionError,
            openai.InternalServerError,
        )
    )


def youtube_retry(max_attempts: int = 2):
    """Retry decorator specifically for YouTube operations."""
    return retry_decorator(
        max_attempts=max_attempts,
        delay=2.0,
        backoff_factor=1.5,
        exceptions=(ConnectionError, TimeoutError, Exception)
    )


def file_operation_retry(max_attempts: int = 2):
    """Retry decorator for file operations."""
    return retry_decorator(
        max_attempts=max_attempts,
        delay=0.5,
        backoff_factor=1.5,
        exceptions=(OSError, IOError, PermissionError)
    )
