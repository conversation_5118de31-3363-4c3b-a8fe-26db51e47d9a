"""Subtitle management endpoints for Helios subtitle corrector."""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.project import Project
from app.models.subtitle_segment import SubtitleSegment
from app.models.correction_suggestion import CorrectionSuggestion
from app.schemas.subtitle_segment import SubtitleSegmentResponse, SubtitleSegmentUpdate
from app.schemas.correction_suggestion import CorrectionSuggestionResponse, CorrectionSuggestionCreate

router = APIRouter(prefix="/subtitles", tags=["subtitles"])


@router.get("/project/{project_id}", response_model=List[SubtitleSegmentResponse])
async def get_project_subtitles(
    project_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(1000, ge=1, le=10000),
    include_corrections: bool = Query(True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all subtitle segments for a project."""
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    query = db.query(SubtitleSegment).filter(
        SubtitleSegment.project_id == project_id
    ).order_by(SubtitleSegment.start_time)
    
    segments = query.offset(skip).limit(limit).all()
    
    if include_corrections:
        # Eager load corrections
        for segment in segments:
            segment.correction_suggestions
    
    return segments


@router.get("/segment/{segment_id}", response_model=SubtitleSegmentResponse)
async def get_subtitle_segment(
    segment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific subtitle segment."""
    segment = db.query(SubtitleSegment).join(Project).filter(
        SubtitleSegment.id == segment_id,
        Project.user_id == current_user.id
    ).first()
    
    if not segment:
        raise HTTPException(status_code=404, detail="Subtitle segment not found")
    
    return segment


@router.put("/segment/{segment_id}", response_model=SubtitleSegmentResponse)
async def update_subtitle_segment(
    segment_id: int,
    segment_update: SubtitleSegmentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a subtitle segment."""
    segment = db.query(SubtitleSegment).join(Project).filter(
        SubtitleSegment.id == segment_id,
        Project.user_id == current_user.id
    ).first()
    
    if not segment:
        raise HTTPException(status_code=404, detail="Subtitle segment not found")
    
    update_data = segment_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(segment, field, value)
    
    db.commit()
    db.refresh(segment)
    return segment


@router.post("/segment/{segment_id}/corrections", response_model=CorrectionSuggestionResponse)
async def create_correction_suggestion(
    segment_id: int,
    correction: CorrectionSuggestionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new correction suggestion for a subtitle segment."""
    segment = db.query(SubtitleSegment).join(Project).filter(
        SubtitleSegment.id == segment_id,
        Project.user_id == current_user.id
    ).first()
    
    if not segment:
        raise HTTPException(status_code=404, detail="Subtitle segment not found")
    
    db_correction = CorrectionSuggestion(
        subtitle_segment_id=segment_id,
        original_text=correction.original_text,
        suggested_text=correction.suggested_text,
        reason=correction.reason,
        confidence=correction.confidence,
        correction_type=correction.correction_type,
        is_accepted=False
    )
    
    db.add(db_correction)
    db.commit()
    db.refresh(db_correction)
    return db_correction


@router.get("/segment/{segment_id}/corrections", response_model=List[CorrectionSuggestionResponse])
async def get_correction_suggestions(
    segment_id: int,
    include_accepted: bool = Query(True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all correction suggestions for a subtitle segment."""
    segment = db.query(SubtitleSegment).join(Project).filter(
        SubtitleSegment.id == segment_id,
        Project.user_id == current_user.id
    ).first()
    
    if not segment:
        raise HTTPException(status_code=404, detail="Subtitle segment not found")
    
    query = db.query(CorrectionSuggestion).filter(
        CorrectionSuggestion.subtitle_segment_id == segment_id
    )
    
    if not include_accepted:
        query = query.filter(CorrectionSuggestion.is_accepted == False)
    
    return query.order_by(CorrectionSuggestion.confidence.desc()).all()


@router.put("/corrections/{correction_id}/accept", response_model=CorrectionSuggestionResponse)
async def accept_correction(
    correction_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Accept a correction suggestion."""
    correction = db.query(CorrectionSuggestion).join(
        SubtitleSegment
    ).join(Project).filter(
        CorrectionSuggestion.id == correction_id,
        Project.user_id == current_user.id
    ).first()
    
    if not correction:
        raise HTTPException(status_code=404, detail="Correction suggestion not found")
    
    # Accept this correction and reject others for the same segment
    correction.is_accepted = True
    
    # Update the segment text
    segment = correction.subtitle_segment
    segment.corrected_text = correction.suggested_text
    
    # Reject other suggestions for this segment
    db.query(CorrectionSuggestion).filter(
        CorrectionSuggestion.subtitle_segment_id == segment.id,
        CorrectionSuggestion.id != correction_id
    ).update({"is_accepted": False})
    
    db.commit()
    db.refresh(correction)
    return correction


@router.put("/corrections/{correction_id}/reject", response_model=CorrectionSuggestionResponse)
async def reject_correction(
    correction_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Reject a correction suggestion."""
    correction = db.query(CorrectionSuggestion).join(
        SubtitleSegment
    ).join(Project).filter(
        CorrectionSuggestion.id == correction_id,
        Project.user_id == current_user.id
    ).first()
    
    if not correction:
        raise HTTPException(status_code=404, detail="Correction suggestion not found")
    
    correction.is_accepted = False
    db.commit()
    db.refresh(correction)
    return correction


@router.get("/export/{project_id}")
async def export_subtitles(
    project_id: int,
    format: str = Query("srt", regex="^(srt|vtt|txt)$"),
    include_corrections: bool = Query(True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Export subtitles in specified format."""
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    segments = db.query(SubtitleSegment).filter(
        SubtitleSegment.project_id == project_id
    ).order_by(SubtitleSegment.start_time).all()
    
    if not segments:
        raise HTTPException(status_code=404, detail="No subtitles found")
    
    # Generate subtitle content based on format
    content = ""
    
    if format == "srt":
        for i, segment in enumerate(segments, 1):
            text = segment.corrected_text or segment.original_text
            start_time = segment.start_time
            end_time = segment.end_time
            
            # Format time as SRT format (HH:MM:SS,mmm)
            start_str = f"{int(start_time//3600):02d}:{int((start_time%3600)//60):02d}:{int(start_time%60):02d},{int((start_time%1)*1000):03d}"
            end_str = f"{int(end_time//3600):02d}:{int((end_time%3600)//60):02d}:{int(end_time%60):02d},{int((end_time%1)*1000):03d}"
            
            content += f"{i}\n{start_str} --> {end_str}\n{text}\n\n"
    
    elif format == "vtt":
        content = "WEBVTT\n\n"
        for segment in segments:
            text = segment.corrected_text or segment.original_text
            start_time = segment.start_time
            end_time = segment.end_time
            
            start_str = f"{int(start_time//3600):02d}:{int((start_time%3600)//60):02d}:{int(start_time%60):02d}.{int((start_time%1)*1000):03d}"
            end_str = f"{int(end_time//3600):02d}:{int((end_time%3600)//60):02d}:{int(end_time%60):02d}.{int((end_time%1)*1000):03d}"
            
            content += f"{start_str} --> {end_str}\n{text}\n\n"
    
    elif format == "txt":
        for segment in segments:
            text = segment.corrected_text or segment.original_text
            content += f"{text}\n"
    
    return {
        "filename": f"{project.name}.{format}",
        "content": content,
        "format": format
    }