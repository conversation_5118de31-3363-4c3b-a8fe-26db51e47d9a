"""Project management endpoints for AI Korektor Titulků."""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Body
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas import CreateProjectRequest, Project, ProjectSummary, UpdateSegmentRequest, SubtitleSegment
from app.services.project_service import ProjectService
from app.services.youtube_service import YouTubeService
from app.services.advanced_export_service import AdvancedExportService
from app.models import Project as ProjectModel

router = APIRouter(prefix="/projects", tags=["projects"])


@router.post("/", response_model=Project)
async def create_project(
    project_data: CreateProjectRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Vytvoří nový projekt a spustí zpracování na pozadí
    """
    # Validace YouTube URL
    if not YouTubeService.is_valid_youtube_url(project_data.youtube_url):
        raise HTTPException(status_code=400, detail="Invalid YouTube URL")

    # Vytvoř projekt v databázi
    project_service = ProjectService(db)
    project = project_service.create_project(project_data.youtube_url)

    # Pouze načti metadata videa - žádné automatické zpracování
    try:
        youtube_service = YouTubeService()
        video_info = youtube_service.get_video_info(project.youtube_url)

        # Aktualizuj pouze základní informace
        project.video_title = video_info.get("title", "Neznámý název")
        project.video_duration = video_info.get("duration", 0)
        project.status = "loaded"  # Nový status - video načteno, čeká na akce
        db.commit()

    except Exception as e:
        project.status = "error"
        project.error_message = f"Chyba při načítání videa: {str(e)}"
        db.commit()
        raise HTTPException(status_code=500, detail=f"Chyba při načítání videa: {str(e)}")

    return project


@router.get("/", response_model=List[ProjectSummary])
async def get_projects(db: Session = Depends(get_db)):
    """
    Vrátí seznam všech projektů bez detailů segmentů
    """
    project_service = ProjectService(db)
    return project_service.get_all_projects()

@router.get("/{project_id}", response_model=Project)
async def get_project(project_id: str, db: Session = Depends(get_db)):
    """
    Vrátí detailní informace o projektu včetně segmentů
    """
    project_service = ProjectService(db)
    project = project_service.get_project_by_id(project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project


@router.put("/{project_id}/segments/{segment_id}", response_model=SubtitleSegment)
async def update_segment(
    project_id: str,
    segment_id: str,
    update_data: UpdateSegmentRequest,
    db: Session = Depends(get_db)
):
    """
    Aktualizuje text segmentu (uživatelská editace)
    """
    project_service = ProjectService(db)
    segment = project_service.update_segment_text(segment_id, update_data.corrected_text)
    if not segment:
        raise HTTPException(status_code=404, detail="Segment not found")
    return segment

@router.post("/{project_id}/segments/{segment_id}/suggestions/{suggestion_id}/apply")
async def apply_suggestion(
    project_id: str,
    segment_id: str,
    suggestion_id: str,
    db: Session = Depends(get_db)
):
    """
    Aplikuje konkrétní návrh korekce
    """
    project_service = ProjectService(db)
    success = project_service.apply_correction_suggestion(suggestion_id)
    if not success:
        raise HTTPException(status_code=404, detail="Suggestion not found")
    return {"success": True}

@router.delete("/{project_id}")
async def delete_project(project_id: str, db: Session = Depends(get_db)):
    """
    Smaže projekt a všechna související data
    """
    project_service = ProjectService(db)
    success = project_service.delete_project(project_id)
    if not success:
        raise HTTPException(status_code=404, detail="Project not found")
    return {"message": "Project deleted successfully"}


@router.get("/{project_id}/export/preview")
async def preview_export(project_id: str, db: Session = Depends(get_db)):
    """
    Vrátí preview SRT exportu s validací a statistikami
    """
    project_service = ProjectService(db)
    export_data = project_service.generate_srt_file(project_id, include_preview=True)
    if not export_data:
        raise HTTPException(status_code=404, detail="Project not found or no segments")

    return export_data

@router.post("/{project_id}/export")
async def export_project(project_id: str, db: Session = Depends(get_db)):
    """
    Exportuje projekt do SRT formátu
    """
    from fastapi.responses import Response

    project_service = ProjectService(db)
    export_data = project_service.generate_srt_file(project_id)
    if not export_data:
        raise HTTPException(status_code=404, detail="Project not found or no segments")

    return Response(
        content=export_data["content"],
        media_type="text/plain",
        headers={"Content-Disposition": f"attachment; filename={export_data['filename']}"}
    )

# Nové endpointy pro manuální akce

@router.get("/{project_id}/available-subtitles")
async def get_available_subtitles(project_id: str, db: Session = Depends(get_db)):
    """
    Vrátí seznam dostupných titulků pro video
    """
    project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        youtube_service = YouTubeService()
        available_subtitles = youtube_service.get_available_subtitle_languages(project.youtube_url)
        return {"available_languages": available_subtitles}
    except Exception as e:
        error_msg = str(e)
        if "private" in error_msg.lower() or "unavailable" in error_msg.lower() or "není dostupné" in error_msg:
            # Aktualizuj projekt status
            project.status = "error"
            project.error_message = "Video není dostupné (soukromé nebo smazané)"
            db.commit()
            raise HTTPException(status_code=403, detail="Video není dostupné (soukromé nebo smazané)")
        raise HTTPException(status_code=500, detail=f"Chyba při načítání titulků: {error_msg}")

@router.post("/{project_id}/extract-subtitles")
async def extract_subtitles(
    project_id: str,
    language: str = "cs",
    db: Session = Depends(get_db)
):
    """
    Extrahuje titulky z YouTube videa v zadaném jazyce
    """
    project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        youtube_service = YouTubeService()
        subtitles = youtube_service.extract_subtitles(project.youtube_url, language)

        if not subtitles:
            return {"message": f"Žádné titulky v jazyce '{language}' nebyly nalezeny", "segments_count": 0}

        # Ulož extrahované titulky jako segmenty
        from app.models import SubtitleSegment as SegmentModel
        import uuid

        # Smaž existující segmenty
        db.query(SegmentModel).filter(SegmentModel.project_id == project_id).delete()

        # Přidej nové segmenty z titulků
        for i, subtitle in enumerate(subtitles):
            segment_model = SegmentModel(
                segment_id=str(uuid.uuid4()),
                project_id=project_id,
                sequence_number=subtitle.get('sequence', i + 1),
                start_time_ms=subtitle.get('start_time_ms', 0),
                end_time_ms=subtitle.get('end_time_ms', 0),
                original_text=subtitle.get('text', ''),
                corrected_text=subtitle.get('text', ''),
                status="extracted"
            )
            db.add(segment_model)

        project.status = "subtitles_extracted"
        db.commit()

        return {"message": f"Titulky v jazyce '{language}' úspěšně extrahovány", "segments_count": len(subtitles)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při extrakci titulků: {str(e)}")

@router.post("/{project_id}/download-audio")
async def download_audio(project_id: str, db: Session = Depends(get_db)):
    """
    Stáhne audio z YouTube videa
    """
    project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        youtube_service = YouTubeService()
        from app.core.config import settings
        from pathlib import Path

        temp_dir = Path(settings.TEMP_DIR)
        audio_file_path = youtube_service.download_audio(
            project.youtube_url,
            str(temp_dir),
            project_id
        )

        project.status = "audio_downloaded"
        project.error_message = None
        db.commit()

        return {"message": "Audio úspěšně staženo", "audio_file": audio_file_path}

    except Exception as e:
        project.status = "error"
        project.error_message = f"Chyba při stahování audio: {str(e)}"
        db.commit()
        raise HTTPException(status_code=500, detail=f"Chyba při stahování audio: {str(e)}")

@router.post("/{project_id}/transcribe-whisper")
async def transcribe_with_whisper(project_id: str, db: Session = Depends(get_db)):
    """
    Provede Whisper transkripci audio souboru
    """
    project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        # Pro testování - mock Whisper transkripce
        from app.models import SubtitleSegment as SegmentModel
        import uuid

        # Smaž existující segmenty
        db.query(SegmentModel).filter(SegmentModel.project_id == project_id).delete()

        # Mock Whisper segmenty
        whisper_segments = [
            {
                'start_time': 0.0,
                'end_time': 4.2,
                'text': 'Vítejte v našem videu o umělé inteligenci.'
            },
            {
                'start_time': 4.2,
                'end_time': 8.5,
                'text': 'Dnes si ukážeme, jak funguje zpracování přirozeného jazyka.'
            },
            {
                'start_time': 8.5,
                'end_time': 12.8,
                'text': 'Whisper je pokročilý model pro převod řeči na text.'
            }
        ]

        # Přidej Whisper segmenty
        for i, segment in enumerate(whisper_segments):
            segment_model = SegmentModel(
                segment_id=str(uuid.uuid4()),
                project_id=project_id,
                sequence_number=i + 1,
                start_time_ms=int(segment['start_time'] * 1000),
                end_time_ms=int(segment['end_time'] * 1000),
                original_text=segment['text'],
                corrected_text=segment['text'],
                status="whisper_transcribed"
            )
            db.add(segment_model)

        project.status = "whisper_transcribed"
        db.commit()

        return {"message": "Whisper transkripce dokončena", "segments_count": len(whisper_segments)}

    except Exception as e:
        project.status = "error"
        project.error_message = f"Chyba při Whisper transkripci: {str(e)}"
        db.commit()
        raise HTTPException(status_code=500, detail=f"Chyba při Whisper transkripci: {str(e)}")

@router.post("/{project_id}/correct-ai")
async def correct_with_ai(project_id: str, db: Session = Depends(get_db)):
    """
    Provede AI korekci existujících segmentů
    """
    project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        from app.models import SubtitleSegment as SegmentModel

        # Načti existující segmenty
        segments = db.query(SegmentModel).filter(
            SegmentModel.project_id == project_id
        ).order_by(SegmentModel.sequence_number).all()

        if not segments:
            raise HTTPException(status_code=400, detail="Žádné segmenty k opravě. Nejdříve extrahujte titulky nebo proveďte Whisper transkripci.")

        # Mock AI korekce
        corrections_made = 0
        for segment in segments:
            # Simulace AI korekce - oprav některé běžné chyby
            original = segment.original_text
            corrected = original

            # Simulace oprav
            corrections = {
                'umela': 'umělá',
                'inteligence': 'inteligence',
                'zpracovani': 'zpracování',
                'prirodzeny': 'přirozený',
                'jazyk': 'jazyk',
                'model': 'model',
                'prevod': 'převod',
                'reci': 'řeči',
                'text': 'text'
            }

            for wrong, right in corrections.items():
                if wrong in corrected.lower():
                    corrected = corrected.replace(wrong, right)
                    corrections_made += 1

            segment.corrected_text = corrected
            segment.status = "ai_corrected" if corrected != original else "unchanged"

        project.status = "ai_corrected"
        db.commit()

        return {"message": f"AI korekce dokončena. Provedeno {corrections_made} oprav.", "segments_count": len(segments)}

    except Exception as e:
        project.status = "error"
        project.error_message = f"Chyba při AI korekci: {str(e)}"
        db.commit()
        raise HTTPException(status_code=500, detail=f"Chyba při AI korekci: {str(e)}")


@router.post("/{project_id}/export-advanced")
async def export_project_advanced(
    project_id: str,
    export_options: dict = Body(...),
    db: Session = Depends(get_db)
):
    """
    Pokročilý export projektu s možnostmi formátu, kódování a výběru segmentů
    """
    try:
        export_service = AdvancedExportService()

        # Extrahuj možnosti z request body
        format_type = export_options.get('format', 'srt')
        encoding = export_options.get('encoding', 'utf-8')
        include_original = export_options.get('include_original', False)
        include_timestamps = export_options.get('include_timestamps', True)
        selected_segments = export_options.get('selected_segments')
        custom_options = export_options.get('custom_options', {})

        # Proveď export
        result = await export_service.export_subtitles(
            project_id=project_id,
            db=db,
            format_type=format_type,
            encoding=encoding,
            include_original=include_original,
            include_timestamps=include_timestamps,
            selected_segments=selected_segments,
            custom_options=custom_options
        )

        # Vrať výsledek podle požadovaného formátu
        if export_options.get('return_content', False):
            # Vrať obsah přímo v JSON
            return result
        else:
            # Vrať jako soubor ke stažení
            from fastapi.responses import Response
            return Response(
                content=result["content"].encode(encoding),
                media_type="application/octet-stream",
                headers={
                    "Content-Disposition": f"attachment; filename={result['filename']}",
                    "Content-Type": f"text/plain; charset={encoding}"
                }
            )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při exportu: {str(e)}")


@router.get("/{project_id}/export-options")
async def get_export_options(project_id: str, db: Session = Depends(get_db)):
    """
    Získá dostupné možnosti exportu pro projekt
    """
    try:
        # Zkontroluj, zda projekt existuje
        project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Spočítej segmenty
        from app.models.subtitle_segment import SubtitleSegment as SegmentModel
        segments_count = db.query(SegmentModel).filter(
            SegmentModel.project_id == project_id
        ).count()

        return {
            "supported_formats": list(AdvancedExportService.SUPPORTED_FORMATS),
            "supported_encodings": list(AdvancedExportService.SUPPORTED_ENCODINGS),
            "project_info": {
                "project_id": project_id,
                "title": project.video_title,
                "segments_count": segments_count,
                "status": project.status
            },
            "format_descriptions": {
                "srt": "SubRip Subtitle format - nejběžnější formát pro titulky",
                "vtt": "WebVTT format - moderní webový formát titulků",
                "txt": "Prostý text - pouze text bez časování",
                "json": "JSON format - strukturovaná data s metadaty",
                "csv": "CSV format - tabulková data pro analýzu"
            },
            "encoding_descriptions": {
                "utf-8": "Unicode UTF-8 (doporučeno)",
                "utf-16": "Unicode UTF-16",
                "ascii": "ASCII (pouze anglické znaky)",
                "iso-8859-1": "Latin-1 (západoevropské znaky)"
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při získávání možností exportu: {str(e)}")