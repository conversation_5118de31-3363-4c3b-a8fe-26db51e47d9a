"""User dictionary management endpoints for AI Korektor Titulků."""

from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas import UserDictionaryTerm, CreateDictionaryTermRequest
from app.services.dictionary_service import DictionaryService

dictionary_router = APIRouter(prefix="/dictionary", tags=["dictionary"])


@dictionary_router.get("/", response_model=List[UserDictionaryTerm])
async def get_dictionary(db: Session = Depends(get_db)):
    """
    Vrátí všechny termíny z uživatelského slovníku
    """
    dictionary_service = DictionaryService(db)
    return dictionary_service.get_all_dictionary_terms()

@dictionary_router.post("/", response_model=UserDictionaryTerm)
async def add_dictionary_term(
    term_data: CreateDictionaryTermRequest,
    db: Session = Depends(get_db)
):
    """
    Přidá nový termín do slovníku
    """
    dictionary_service = DictionaryService(db)
    return dictionary_service.create_dictionary_term(term_data)

@dictionary_router.delete("/{term_id}")
async def delete_dictionary_term(term_id: str, db: Session = Depends(get_db)):
    """
    Smaže termín ze slovníku
    """
    dictionary_service = DictionaryService(db)
    success = dictionary_service.delete_dictionary_term_by_id(term_id)
    if not success:
        raise HTTPException(status_code=404, detail="Term not found")
    return {"success": True}


