"""WebSocket endpoints for real-time updates."""

import json
import logging
from datetime import datetime
from typing import Dict, Set
from fastapi import <PERSON><PERSON><PERSON>er, WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

logger = logging.getLogger(__name__)

router = APIRouter()

# Store active WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, project_id: str):
        """Connect a WebSocket to a project"""
        await websocket.accept()
        
        if project_id not in self.active_connections:
            self.active_connections[project_id] = set()
        
        self.active_connections[project_id].add(websocket)
        logger.info(f"WebSocket connected for project {project_id}")
    
    def disconnect(self, websocket: WebSocket, project_id: str):
        """Disconnect a WebSocket from a project"""
        if project_id in self.active_connections:
            self.active_connections[project_id].discard(websocket)
            
            # Clean up empty project connections
            if not self.active_connections[project_id]:
                del self.active_connections[project_id]
        
        logger.info(f"WebSocket disconnected for project {project_id}")
    
    async def send_progress_update(self, project_id: str, message: str, progress: float = None):
        """Send progress update to all connected clients for a project"""
        if project_id not in self.active_connections:
            return
        
        update_data = {
            "type": "progress",
            "project_id": project_id,
            "message": message,
            "progress": progress,
            "timestamp": str(datetime.utcnow())
        }
        
        # Send to all connected clients for this project
        disconnected_clients = []
        for websocket in self.active_connections[project_id].copy():
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_text(json.dumps(update_data))
                else:
                    disconnected_clients.append(websocket)
            except Exception as e:
                logger.error(f"Error sending WebSocket message: {e}")
                disconnected_clients.append(websocket)
        
        # Clean up disconnected clients
        for websocket in disconnected_clients:
            self.active_connections[project_id].discard(websocket)
    
    async def send_status_update(self, project_id: str, status: str, error_message: str = None):
        """Send status update to all connected clients for a project"""
        if project_id not in self.active_connections:
            return
        
        update_data = {
            "type": "status",
            "project_id": project_id,
            "status": status,
            "error_message": error_message,
            "timestamp": str(datetime.utcnow())
        }
        
        # Send to all connected clients for this project
        disconnected_clients = []
        for websocket in self.active_connections[project_id].copy():
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_text(json.dumps(update_data))
                else:
                    disconnected_clients.append(websocket)
            except Exception as e:
                logger.error(f"Error sending WebSocket message: {e}")
                disconnected_clients.append(websocket)
        
        # Clean up disconnected clients
        for websocket in disconnected_clients:
            self.active_connections[project_id].discard(websocket)

# Global connection manager instance
manager = ConnectionManager()

@router.websocket("/ws/projects/{project_id}")
async def websocket_endpoint(websocket: WebSocket, project_id: str):
    """WebSocket endpoint for project updates"""
    await manager.connect(websocket, project_id)
    
    try:
        while True:
            # Keep connection alive and listen for client messages
            data = await websocket.receive_text()
            
            # Handle client messages if needed
            try:
                message = json.loads(data)
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
            except json.JSONDecodeError:
                pass
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, project_id)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket, project_id)

# Export manager for use in other services
__all__ = ["router", "manager"]
