"""Monitoring API endpoints."""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional
import logging

from app.utils.monitoring import performance_monitor, get_performance_report

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/metrics")
async def get_metrics(operation: Optional[str] = None) -> Dict[str, Any]:
    """
    Get performance metrics for operations.
    
    Args:
        operation: Optional specific operation name to get metrics for
        
    Returns:
        Performance metrics data
    """
    try:
        if operation:
            metrics = performance_monitor.get_metrics(operation)
            if not metrics:
                raise HTTPException(
                    status_code=404,
                    detail=f"No metrics found for operation: {operation}"
                )
            return {"operation": operation, "metrics": metrics}
        else:
            return {"metrics": performance_monitor.get_metrics()}
    except Exception as e:
        logger.error(f"Error getting metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get metrics")


@router.get("/metrics/summary")
async def get_metrics_summary() -> Dict[str, Any]:
    """
    Get summary of all performance metrics.
    
    Returns:
        Summary of performance metrics
    """
    try:
        return performance_monitor.get_summary()
    except Exception as e:
        logger.error(f"Error getting metrics summary: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get metrics summary")


@router.get("/health/detailed")
async def get_detailed_health() -> Dict[str, Any]:
    """
    Get detailed health and performance report.
    
    Returns:
        Comprehensive health and performance data
    """
    try:
        return get_performance_report()
    except Exception as e:
        logger.error(f"Error getting detailed health: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get detailed health")


@router.delete("/metrics")
async def clear_metrics(operation: Optional[str] = None) -> Dict[str, str]:
    """
    Clear performance metrics.
    
    Args:
        operation: Optional specific operation to clear metrics for
        
    Returns:
        Success message
    """
    try:
        if operation:
            if operation in performance_monitor.metrics:
                del performance_monitor.metrics[operation]
                return {"message": f"Metrics cleared for operation: {operation}"}
            else:
                raise HTTPException(
                    status_code=404,
                    detail=f"No metrics found for operation: {operation}"
                )
        else:
            performance_monitor.metrics.clear()
            return {"message": "All metrics cleared"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to clear metrics")
