"""Authentication endpoints."""
from datetime import timed<PERSON><PERSON>
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON><PERSON>RequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from backend.app.core import security
from backend.app.core.config import settings
from backend.app.core.database import get_db
from backend.app.models.user import User
from backend.app.schemas.user import User<PERSON><PERSON>, UserResponse, Token

router = APIRouter()


@router.post("/register", response_model=UserResponse)
async def register(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Register a new user."""
    # Check if user already exists
    existing_user = await User.get_by_email(db, user_data.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create new user
    user = await User.create(db, user_data)
    return user


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Login with email and password."""
    user = await User.authenticate(
        db,
        email=form_data.username,
        password=form_data.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password"
        )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }


@router.post("/refresh", response_model=Token)
async def refresh_token(
    current_user: User = Depends(security.get_current_user)
) -> Any:
    """Refresh access token."""
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        data={"sub": str(current_user.id)},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    current_user: User = Depends(security.get_current_user)
) -> Any:
    """Get current user information."""
    return current_user


@router.post("/api-key")
async def generate_api_key(
    current_user: User = Depends(security.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Generate a new API key for the user."""
    api_key = security.generate_api_key()
    current_user.api_key = security.hash_api_key(api_key)
    await db.commit()
    
    return {"api_key": api_key}


@router.delete("/api-key")
async def revoke_api_key(
    current_user: User = Depends(security.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Revoke the current API key."""
    current_user.api_key = None
    await db.commit()
    
    return {"message": "API key revoked"}