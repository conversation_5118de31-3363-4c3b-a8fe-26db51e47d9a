"""User dictionary management endpoints for Helios backend."""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.user_dictionary import UserDictionary
from app.schemas.user_dictionary import (
    UserDictionaryCreate,
    UserDictionaryResponse,
    UserDictionaryUpdate
)

router = APIRouter(prefix="/dictionary", tags=["dictionary"])


@router.post("/", response_model=UserDictionaryResponse)
async def create_dictionary_entry(
    entry: UserDictionaryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new dictionary entry."""
    # Check if entry already exists
    existing = db.query(UserDictionary).filter(
        UserDictionary.user_id == current_user.id,
        UserDictionary.original_word == entry.original_word
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=400,
            detail="Dictionary entry already exists for this word"
        )
    
    db_entry = UserDictionary(
        **entry.dict(),
        user_id=current_user.id
    )
    db.add(db_entry)
    db.commit()
    db.refresh(db_entry)
    return db_entry


@router.get("/", response_model=List[UserDictionaryResponse])
async def get_dictionary_entries(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="Search in original or corrected word"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all dictionary entries for the current user."""
    query = db.query(UserDictionary).filter(
        UserDictionary.user_id == current_user.id
    )
    
    if search:
        query = query.filter(
            UserDictionary.original_word.contains(search) |
            UserDictionary.corrected_word.contains(search)
        )
    
    entries = query.offset(skip).limit(limit).all()
    return entries


@router.get("/{entry_id}", response_model=UserDictionaryResponse)
async def get_dictionary_entry(
    entry_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific dictionary entry."""
    entry = db.query(UserDictionary).filter(
        UserDictionary.id == entry_id,
        UserDictionary.user_id == current_user.id
    ).first()
    
    if not entry:
        raise HTTPException(status_code=404, detail="Dictionary entry not found")
    
    return entry


@router.put("/{entry_id}", response_model=UserDictionaryResponse)
async def update_dictionary_entry(
    entry_id: int,
    entry_update: UserDictionaryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a dictionary entry."""
    entry = db.query(UserDictionary).filter(
        UserDictionary.id == entry_id,
        UserDictionary.user_id == current_user.id
    ).first()
    
    if not entry:
        raise HTTPException(status_code=404, detail="Dictionary entry not found")
    
    for field, value in entry_update.dict(exclude_unset=True).items():
        setattr(entry, field, value)
    
    db.commit()
    db.refresh(entry)
    return entry


@router.delete("/{entry_id}")
async def delete_dictionary_entry(
    entry_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a dictionary entry."""
    entry = db.query(UserDictionary).filter(
        UserDictionary.id == entry_id,
        UserDictionary.user_id == current_user.id
    ).first()
    
    if not entry:
        raise HTTPException(status_code=404, detail="Dictionary entry not found")
    
    db.delete(entry)
    db.commit()
    
    return {"message": "Dictionary entry deleted successfully"}


@router.get("/search/{word}")
async def search_dictionary(
    word: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Search for a word in the dictionary."""
    entry = db.query(UserDictionary).filter(
        UserDictionary.user_id == current_user.id,
        UserDictionary.original_word == word
    ).first()
    
    if entry:
        return {
            "found": True,
            "entry": entry
        }
    
    # Check for partial matches
    partial_matches = db.query(UserDictionary).filter(
        UserDictionary.user_id == current_user.id,
        UserDictionary.original_word.contains(word)
    ).limit(5).all()
    
    return {
        "found": False,
        "partial_matches": partial_matches
    }


@router.post("/bulk-import")
async def bulk_import_dictionary(
    entries: List[UserDictionaryCreate],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Bulk import dictionary entries."""
    created_entries = []
    
    for entry_data in entries:
        # Check if entry already exists
        existing = db.query(UserDictionary).filter(
            UserDictionary.user_id == current_user.id,
            UserDictionary.original_word == entry_data.original_word
        ).first()
        
        if existing:
            continue
        
        db_entry = UserDictionary(
            **entry_data.dict(),
            user_id=current_user.id
        )
        db.add(db_entry)
        created_entries.append(db_entry)
    
    db.commit()
    
    for entry in created_entries:
        db.refresh(entry)
    
    return {
        "message": f"Imported {len(created_entries)} dictionary entries",
        "entries": created_entries
    }


@router.get("/export")
async def export_dictionary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Export all dictionary entries."""
    entries = db.query(UserDictionary).filter(
        UserDictionary.user_id == current_user.id
    ).all()
    
    return {
        "entries": entries,
        "count": len(entries)
    }