"""Subtitle management endpoints for <PERSON><PERSON><PERSON> backend."""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.subtitle_segment import SubtitleSegment
from app.models.correction_suggestion import CorrectionSuggestion
from app.schemas.subtitle_segment import SubtitleSegmentResponse, SubtitleSegmentUpdate
from app.schemas.correction_suggestion import CorrectionSuggestionResponse, CorrectionSuggestionCreate

router = APIRouter(prefix="/subtitles", tags=["subtitles"])


@router.get("/project/{project_id}", response_model=List[SubtitleSegmentResponse])
async def get_subtitle_segments(
    project_id: int,
    skip: int = 0,
    limit: int = 1000,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all subtitle segments for a project."""
    segments = db.query(SubtitleSegment).join(
        SubtitleSegment.project
    ).filter(
        SubtitleSegment.project_id == project_id,
        SubtitleSegment.project.has(user_id=current_user.id)
    ).offset(skip).limit(limit).all()
    
    return segments


@router.get("/{segment_id}", response_model=SubtitleSegmentResponse)
async def get_subtitle_segment(
    segment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific subtitle segment."""
    segment = db.query(SubtitleSegment).join(
        SubtitleSegment.project
    ).filter(
        SubtitleSegment.id == segment_id,
        SubtitleSegment.project.has(user_id=current_user.id)
    ).first()
    
    if not segment:
        raise HTTPException(status_code=404, detail="Subtitle segment not found")
    
    return segment


@router.put("/{segment_id}", response_model=SubtitleSegmentResponse)
async def update_subtitle_segment(
    segment_id: int,
    segment_update: SubtitleSegmentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a subtitle segment."""
    segment = db.query(SubtitleSegment).join(
        SubtitleSegment.project
    ).filter(
        SubtitleSegment.id == segment_id,
        SubtitleSegment.project.has(user_id=current_user.id)
    ).first()
    
    if not segment:
        raise HTTPException(status_code=404, detail="Subtitle segment not found")
    
    for field, value in segment_update.dict(exclude_unset=True).items():
        setattr(segment, field, value)
    
    db.commit()
    db.refresh(segment)
    return segment


@router.get("/{segment_id}/corrections", response_model=List[CorrectionSuggestionResponse])
async def get_corrections(
    segment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all correction suggestions for a subtitle segment."""
    corrections = db.query(CorrectionSuggestion).join(
        CorrectionSuggestion.subtitle_segment
    ).join(
        SubtitleSegment.project
    ).filter(
        CorrectionSuggestion.subtitle_segment_id == segment_id,
        SubtitleSegment.project.has(user_id=current_user.id)
    ).all()
    
    return corrections


@router.post("/{segment_id}/corrections", response_model=CorrectionSuggestionResponse)
async def create_correction(
    segment_id: int,
    correction: CorrectionSuggestionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new correction suggestion for a subtitle segment."""
    segment = db.query(SubtitleSegment).join(
        SubtitleSegment.project
    ).filter(
        SubtitleSegment.id == segment_id,
        SubtitleSegment.project.has(user_id=current_user.id)
    ).first()
    
    if not segment:
        raise HTTPException(status_code=404, detail="Subtitle segment not found")
    
    db_correction = CorrectionSuggestion(
        **correction.dict(),
        subtitle_segment_id=segment_id
    )
    db.add(db_correction)
    db.commit()
    db.refresh(db_correction)
    
    return db_correction


@router.put("/{segment_id}/corrections/{correction_id}", response_model=CorrectionSuggestionResponse)
async def update_correction(
    segment_id: int,
    correction_id: int,
    correction_update: CorrectionSuggestionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a correction suggestion."""
    correction = db.query(CorrectionSuggestion).join(
        CorrectionSuggestion.subtitle_segment
    ).join(
        SubtitleSegment.project
    ).filter(
        CorrectionSuggestion.id == correction_id,
        CorrectionSuggestion.subtitle_segment_id == segment_id,
        SubtitleSegment.project.has(user_id=current_user.id)
    ).first()
    
    if not correction:
        raise HTTPException(status_code=404, detail="Correction suggestion not found")
    
    for field, value in correction_update.dict(exclude_unset=True).items():
        setattr(correction, field, value)
    
    db.commit()
    db.refresh(correction)
    return correction


@router.delete("/{segment_id}/corrections/{correction_id}")
async def delete_correction(
    segment_id: int,
    correction_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a correction suggestion."""
    correction = db.query(CorrectionSuggestion).join(
        CorrectionSuggestion.subtitle_segment
    ).join(
        SubtitleSegment.project
    ).filter(
        CorrectionSuggestion.id == correction_id,
        CorrectionSuggestion.subtitle_segment_id == segment_id,
        SubtitleSegment.project.has(user_id=current_user.id)
    ).first()
    
    if not correction:
        raise HTTPException(status_code=404, detail="Correction suggestion not found")
    
    db.delete(correction)
    db.commit()
    
    return {"message": "Correction suggestion deleted successfully"}


@router.post("/{segment_id}/apply-correction/{correction_id}")
async def apply_correction(
    segment_id: int,
    correction_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Apply a correction suggestion to a subtitle segment."""
    segment = db.query(SubtitleSegment).join(
        SubtitleSegment.project
    ).filter(
        SubtitleSegment.id == segment_id,
        SubtitleSegment.project.has(user_id=current_user.id)
    ).first()
    
    if not segment:
        raise HTTPException(status_code=404, detail="Subtitle segment not found")
    
    correction = db.query(CorrectionSuggestion).filter(
        CorrectionSuggestion.id == correction_id,
        CorrectionSuggestion.subtitle_segment_id == segment_id
    ).first()
    
    if not correction:
        raise HTTPException(status_code=404, detail="Correction suggestion not found")
    
    # Apply the correction
    segment.corrected_text = correction.suggested_text
    segment.is_corrected = True
    
    db.commit()
    db.refresh(segment)
    
    return segment


@router.get("/export/{project_id}")
async def export_subtitles(
    project_id: int,
    format: str = Query("srt", regex="^(srt|vtt|txt)$"),
    corrected: bool = Query(True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Export subtitles in specified format."""
    segments = db.query(SubtitleSegment).join(
        SubtitleSegment.project
    ).filter(
        SubtitleSegment.project_id == project_id,
        SubtitleSegment.project.has(user_id=current_user.id)
    ).order_by(SubtitleSegment.start_time).all()
    
    if not segments:
        raise HTTPException(status_code=404, detail="No subtitle segments found")
    
    # Generate export content based on format
    if format == "srt":
        content = generate_srt_content(segments, corrected)
    elif format == "vtt":
        content = generate_vtt_content(segments, corrected)
    elif format == "txt":
        content = generate_txt_content(segments, corrected)
    
    return {
        "content": content,
        "format": format,
        "filename": f"subtitles_{project_id}.{format}"
    }


def generate_srt_content(segments: List[SubtitleSegment], corrected: bool) -> str:
    """Generate SRT format content."""
    srt_lines = []
    for i, segment in enumerate(segments, 1):
        text = segment.corrected_text if corrected and segment.corrected_text else segment.original_text
        
        start_time = format_srt_time(segment.start_time)
        end_time = format_srt_time(segment.end_time)
        
        srt_lines.extend([
            str(i),
            f"{start_time} --> {end_time}",
            text,
            ""
        ])
    
    return "\n".join(srt_lines)


def generate_vtt_content(segments: List[SubtitleSegment], corrected: bool) -> str:
    """Generate WebVTT format content."""
    vtt_lines = ["WEBVTT", ""]
    
    for segment in segments:
        text = segment.corrected_text if corrected and segment.corrected_text else segment.original_text
        
        start_time = format_vtt_time(segment.start_time)
        end_time = format_vtt_time(segment.end_time)
        
        vtt_lines.extend([
            f"{start_time} --> {end_time}",
            text,
            ""
        ])
    
    return "\n".join(vtt_lines)


def generate_txt_content(segments: List[SubtitleSegment], corrected: bool) -> str:
    """Generate plain text format content."""
    lines = []
    for segment in segments:
        text = segment.corrected_text if corrected and segment.corrected_text else segment.original_text
        lines.append(text)
    
    return "\n".join(lines)


def format_srt_time(seconds: float) -> str:
    """Format time in SRT format (HH:MM:SS,mmm)."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds % 1) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"


def format_vtt_time(seconds: float) -> str:
    """Format time in WebVTT format (HH:MM:SS.mmm)."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds % 1) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{secs:02d}.{millis:03d}"