from sqlalchemy import Column, String, Integer, DateTime, Text
from sqlalchemy.orm import relationship
from app.core.database import Base
from datetime import datetime


class Project(Base):
    __tablename__ = "projects"

    project_id = Column(String, primary_key=True)
    youtube_url = Column(String, nullable=False)
    video_title = Column(String)
    video_duration = Column(Integer)
    status = Column(String, default="PENDING")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    error_message = Column(Text)

    # Vztahy
    segments = relationship("SubtitleSegment", back_populates="project", cascade="all, delete-orphan")