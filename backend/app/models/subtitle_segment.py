from sqlalchemy import Column, String, Integer, Text, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base


class SubtitleSegment(Base):
    __tablename__ = "subtitle_segments"

    segment_id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey("projects.project_id"), nullable=False)
    sequence_number = Column(Integer, nullable=False)
    start_time_ms = Column(Integer, nullable=False)
    end_time_ms = Column(Integer, nullable=False)
    original_text = Column(Text, nullable=False)
    corrected_text = Column(Text, nullable=False)
    status = Column(String, default="NEEDS_REVIEW")

    # Vztahy
    project = relationship("Project", back_populates="segments")
    suggestions = relationship("CorrectionSuggestion", back_populates="segment", cascade="all, delete-orphan")