"""Subtitle segment schemas for request/response validation."""

from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime


class SubtitleSegmentBase(BaseModel):
    """Base subtitle segment schema."""
    sequence_number: int = Field(..., ge=1)
    start_time: float = Field(..., ge=0)
    end_time: float = Field(..., ge=0)
    original_text: str = Field(..., min_length=1)
    corrected_text: Optional[str] = None
    confidence_score: Optional[float] = Field(None, ge=0, le=1)
    speaker_id: Optional[str] = Field(None, max_length=50)
    is_processed: bool = False


class SubtitleSegmentCreate(SubtitleSegmentBase):
    """Schema for creating a new subtitle segment."""
    project_id: int


class SubtitleSegmentUpdate(BaseModel):
    """Schema for updating subtitle segment."""
    corrected_text: Optional[str] = None
    confidence_score: Optional[float] = Field(None, ge=0, le=1)
    is_processed: Optional[bool] = None


class SubtitleSegmentResponse(SubtitleSegmentBase):
    """Schema for subtitle segment response."""
    id: int
    project_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class SubtitleSegmentList(BaseModel):
    """Schema for subtitle segment list response."""
    items: list[SubtitleSegmentResponse]
    total: int
    project_id: int


class SubtitleExportRequest(BaseModel):
    """Schema for subtitle export request."""
    project_id: int
    format: str = Field(default="srt", pattern="^(srt|vtt|txt)$")
    include_original: bool = False
    include_timestamps: bool = True


class SubtitleImportRequest(BaseModel):
    """Schema for subtitle import request."""
    project_id: int
    subtitle_content: str = Field(..., min_length=1)
    format: str = Field(default="srt", pattern="^(srt|vtt|txt)$")


# Aliases for compatibility
SubtitleSegmentList = BaseModel
SubtitleSegmentSearch = BaseModel
SubtitleSegmentBatchUpdate = BaseModel
SubtitleSegmentExport = SubtitleExportRequest
SubtitleSegmentImport = SubtitleImportRequest