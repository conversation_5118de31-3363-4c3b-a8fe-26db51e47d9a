"""Project schemas for request/response validation."""

from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime


class CreateProjectRequest(BaseModel):
    """Schema for creating a new project."""
    youtube_url: str


class UpdateSegmentRequest(BaseModel):
    """Schema for updating segment text."""
    corrected_text: str


class SubtitleSegment(BaseModel):
    """Schema for subtitle segment."""
    segment_id: str
    sequence_number: int
    start_time_ms: int
    end_time_ms: int
    original_text: str
    corrected_text: str
    status: str
    suggestions: List["CorrectionSuggestion"] = []

    class Config:
        from_attributes = True


class CorrectionSuggestion(BaseModel):
    """Schema for correction suggestion."""
    suggestion_id: str
    type: str
    confidence: float
    description: Optional[str]
    original_fragment: str
    suggested_fragment: str
    applied: bool

    class Config:
        from_attributes = True


class Project(BaseModel):
    """Schema for complete project with segments."""
    project_id: str
    youtube_url: str
    video_title: Optional[str]
    video_duration: Optional[int]
    status: str
    created_at: datetime
    updated_at: datetime
    error_message: Optional[str]
    segments: List[SubtitleSegment] = []

    class Config:
        from_attributes = True


class ProjectSummary(BaseModel):
    """Schema for project summary (without segments)."""
    project_id: str
    youtube_url: str
    video_title: Optional[str]
    video_duration: Optional[int]
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Forward reference fix
SubtitleSegment.model_rebuild()