"""User dictionary schemas for request/response validation."""

from pydantic import BaseModel
from datetime import datetime


class CreateDictionaryTermRequest(BaseModel):
    """Schema for creating a new dictionary term."""
    phrase: str
    case_sensitive: bool = True


class UserDictionaryTerm(BaseModel):
    """Schema for dictionary term response."""
    term_id: str
    phrase: str
    case_sensitive: bool
    created_at: datetime

    class Config:
        from_attributes = True


