{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "isolatedModules": true, "noEmit": false, "declaration": true, "outDir": "./dist", "rootDir": "./src", "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@components/*": ["components/*"], "@services/*": ["services/*"], "@types/*": ["types/*"], "@utils/*": ["utils/*"], "@styles/*": ["styles/*"]}}, "include": ["src/**/*", "electron/**/*"], "exclude": ["node_modules", "dist", "build"]}