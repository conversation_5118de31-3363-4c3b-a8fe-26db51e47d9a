# Helios Frontend - AI Korektor Titulků

Desktopová aplikace pro asistovanou korekturu automaticky generovaných titulků z YouTube, postavená na Electron + React + TypeScript.

## 🚀 Rychlý start

### Požadavky
- Node.js 18+ 
- npm nebo yarn
- B<PERSON>žící backend na `http://localhost:8000`

### Instalace
```bash
# Instalace závislostí
npm install

# Vývojový režim
npm run dev

# Produkční build
npm run build

# Spuštění aplikace
npm start
```

## 📁 Struktura projektu

```
frontend/
├── src/
│   ├── components/          # React komponenty
│   │   ├── MainLayout.tsx   # Hlavní layout aplikace
│   │   ├── ProjectListPanel.tsx  # Seznam projektů
│   │   ├── EditorPanel.tsx     # Editor titulků
│   │   ├── SubtitleSegmentRow.tsx  # Řádek titulku
│   │   ├── ActionPanel.tsx     # Panel akcí a slovníku
│   │   └── DictionaryManager.tsx   # Správa slovníku
│   ├── contexts/            # React Context
│   │   └── ProjectContext.tsx  # State management
│   ├── services/            # API komunikace
│   │   └── api.ts           # API klient
│   ├── types/               # TypeScript typy
│   │   └── index.ts         # Sdílené typy
│   ├── styles/              # Stylování
│   │   └── global.css       # Globální CSS + Tailwind
│   ├── main.ts              # Electron main proces
│   ├── preload.ts           # Electron preload script
│   ├── index.tsx            # React entry point
│   └── App.tsx              # Hlavní React komponenta
├── public/
│   └── index.html           # HTML template
├── webpack.*.config.js      # Webpack konfigurace
├── tailwind.config.js       # Tailwind konfigurace
├── tsconfig.json           # TypeScript konfigurace
└── package.json            # Závislosti a skripty
```

## 🎯 Funkce aplikace

### Hlavní funkce
- **Import YouTube videí** - automatické stažení a zpracování
- **AI korekce titulků** - integrace s OpenAI Whisper + GPT-4
- **Manuální editace** - přímá úprava titulků v aplikaci
- **Video přehrávač** - synchronizace s titulky
- **Export do SRT** - stažení finálních titulků
- **Uživatelský slovník** - správa vlastních termínů

### Klávesové zkratky
- `Ctrl+N` - Nový projekt
- `Ctrl+E` - Export SRT
- `↑/↓` - Přechod mezi segmenty
- `Space` - Play/Pause video
- `Ctrl+Enter` - Uložit editaci
- `Escape` - Zrušit editaci

## 🛠️ Vývoj

### Skripty
```bash
npm run dev          # Vývojový režim s hot reload
npm run build        # Produční build
npm run start        # Spuštění aplikace
npm run package      # Vytvoření instalátorů
npm run package:win  # Windows instalátor
npm run package:mac  # macOS instalátor
npm run package:linux # Linux instalátor
```

### Technologie
- **Electron** - Desktop framework
- **React 18** - UI framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Stylování
- **React Player** - Video přehrávač
- **Lucide React** - Ikony
- **Axios** - HTTP klient

### API komunikace
Frontend komunikuje s backendem přes REST API na `http://localhost:8000/api/v1`:
- `/projects` - správa projektů
- `/dictionary` - uživatelský slovník
- WebSocket pro real-time aktualizace (plánováno)

## 🔧 Konfigurace

### Environment variables
Vytvořte `.env` soubor v kořenovém adresáři:
```bash
# Vývoj
NODE_ENV=development

# Produkcia
NODE_ENV=production
```

### Tailwind CSS
Stylování používá Tailwind CSS s custom komponentami. Hlavní barvy:
- Primary: Blue-600
- Success: Green-600  
- Warning: Orange-600
- Error: Red-600

## 🐛 Řešení problémů

### Backend není dostupný
Ujistěte se, že backend běží na `http://localhost:8000`:
```bash
cd ../backend
python -m app.main
```

### Build chyby
```bash
# Vyčištění cache
rm -rf node_modules dist
npm install
npm run build
```

### Electron problémy
```bash
# Reset Electron cache
rm -rf ~/.config/Electron
```

## 📱 Platformy

Aplikace je kompatibilní s:
- **Windows** 10/11 (NSIS instalátor)
- **macOS** 10.15+ (DMG)
- **Linux** (AppImage)

## 🤝 Contributing

1. Fork repozitáře
2. Vytvořte feature branch
3. Commit změny
4. Push na branch
5. Vytvořte Pull Request

## 📄 Licence

MIT License - viz [LICENSE](../LICENSE) soubor.