import React from 'react';
import { CorrectionSuggestion } from '../types';
interface SuggestionPanelProps {
    suggestions: CorrectionSuggestion[];
    onApplySuggestion: (suggestionId: string) => void;
    onRejectSuggestion: (suggestionId: string) => void;
    onApplyAll: () => void;
    onRejectAll: () => void;
}
declare const SuggestionPanel: React.FC<SuggestionPanelProps>;
export default SuggestionPanel;
