import React from 'react';
import { SubtitleSegment } from '../types';
interface SubtitleSegmentRowProps {
    segment: SubtitleSegment;
    isActive: boolean;
    onSegmentClick: (segmentId: string) => void;
    onTextChange: (segmentId: string, newText: string) => void;
    onApplySuggestion?: (segmentId: string, suggestionId: string) => void;
    onRejectSuggestion?: (segmentId: string, suggestionId: string) => void;
    onEditingChange?: (isEditing: boolean) => void;
}
declare const SubtitleSegmentRow: React.FC<SubtitleSegmentRowProps>;
export default SubtitleSegmentRow;
