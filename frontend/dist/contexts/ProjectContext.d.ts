import React from 'react';
import { Project, ProjectSummary } from '../types';
interface ProjectState {
    projects: ProjectSummary[];
    currentProject: Project | null;
    loading: boolean;
    error: string | null;
}
interface ProjectContextType {
    state: ProjectState;
    loadProjects: () => Promise<void>;
    loadProject: (projectId: string) => Promise<void>;
    createProject: (youtubeUrl: string) => Promise<void>;
    updateSegment: (segmentId: string, correctedText: string) => Promise<void>;
    deleteProject: (projectId: string) => Promise<void>;
    refreshCurrentProject: () => Promise<void>;
}
export declare const ProjectProvider: React.FC<{
    children: React.ReactNode;
}>;
export declare const useProjectContext: () => ProjectContextType;
export {};
