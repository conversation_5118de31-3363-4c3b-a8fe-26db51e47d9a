import { Project, ProjectSummary, SubtitleSegment, UserDictionaryTerm } from '../types';
declare const apiClient: import("axios").AxiosInstance;
export declare const projectsApi: {
    createProject(youtubeUrl: string): Promise<Project>;
    getProjects(): Promise<ProjectSummary[]>;
    getProject(projectId: string): Promise<Project>;
    updateSegment(projectId: string, segmentId: string, correctedText: string): Promise<SubtitleSegment>;
    applySuggestion(projectId: string, segmentId: string, suggestionId: string): Promise<void>;
    getExportPreview(projectId: string): Promise<any>;
    exportProject(projectId: string): Promise<Blob>;
    getAvailableSubtitles(projectId: string): Promise<any>;
    extractSubtitles(projectId: string, language?: string): Promise<any>;
    downloadAudio(projectId: string): Promise<any>;
    transcribeWithWhisper(projectId: string): Promise<any>;
    correctWithAI(projectId: string): Promise<any>;
    deleteProject(projectId: string): Promise<void>;
};
export declare const dictionaryApi: {
    getTerms(): Promise<UserDictionaryTerm[]>;
    addTerm(phrase: string, caseSensitive: boolean): Promise<UserDictionaryTerm>;
    deleteTerm(termId: string): Promise<void>;
};
export default apiClient;
