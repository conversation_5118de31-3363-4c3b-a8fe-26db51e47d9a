/**
 * WebSocket service for real-time project updates
 */
export interface ProgressUpdate {
    type: 'progress';
    project_id: string;
    message: string;
    progress?: number;
    timestamp: string;
}
export interface StatusUpdate {
    type: 'status';
    project_id: string;
    status: string;
    error_message?: string;
    timestamp: string;
}
export type WebSocketMessage = ProgressUpdate | StatusUpdate;
export declare class WebSocketService {
    private baseUrl;
    private ws;
    private projectId;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    private listeners;
    constructor(baseUrl?: string);
    /**
     * Connect to WebSocket for a specific project
     */
    connect(projectId: string): Promise<void>;
    /**
     * Disconnect from WebSocket
     */
    disconnect(): void;
    /**
     * Add listener for WebSocket messages
     */
    addListener(type: string, callback: (message: WebSocketMessage) => void): void;
    /**
     * Remove listener for WebSocket messages
     */
    removeListener(type: string, callback: (message: WebSocketMessage) => void): void;
    /**
     * Send ping to keep connection alive
     */
    ping(): void;
    /**
     * Get connection status
     */
    isConnected(): boolean;
    private handleMessage;
    private scheduleReconnect;
}
export declare const webSocketService: WebSocketService;
export declare function useWebSocket(projectId: string | null): {
    isConnected: boolean;
    lastMessage: WebSocketMessage | null;
};
