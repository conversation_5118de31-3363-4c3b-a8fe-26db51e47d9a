interface KeyboardShortcutsConfig {
    onPlayPause?: () => void;
    onNextSegment?: () => void;
    onPreviousSegment?: () => void;
    onEditSegment?: () => void;
    onAcceptSuggestions?: () => void;
    onRejectSuggestions?: () => void;
    onSaveEdit?: () => void;
    onCancelEdit?: () => void;
    onJumpToSegment?: (time: number) => void;
    onSeekForward?: () => void;
    onSeekBackward?: () => void;
    onToggleFullscreen?: () => void;
    onExport?: () => void;
    onHelp?: () => void;
    isEditing?: boolean;
    disabled?: boolean;
}
export declare const useKeyboardShortcuts: (config: KeyboardShortcutsConfig) => {
    isShortcutActive: boolean;
};
export default useKeyboardShortcuts;
