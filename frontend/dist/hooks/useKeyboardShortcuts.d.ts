interface KeyboardShortcutsConfig {
    onPlayPause?: () => void;
    onNextSegment?: () => void;
    onPreviousSegment?: () => void;
    onEditSegment?: () => void;
    onAcceptSuggestions?: () => void;
    onRejectSuggestions?: () => void;
    onSaveEdit?: () => void;
    onCancelEdit?: () => void;
    isEditing?: boolean;
    disabled?: boolean;
}
export declare const useKeyboardShortcuts: (config: KeyboardShortcutsConfig) => {
    isShortcutActive: boolean;
};
export default useKeyboardShortcuts;
