import { useEffect, useCallback } from 'react';

interface KeyboardShortcutsConfig {
  onPlayPause?: () => void;
  onNextSegment?: () => void;
  onPreviousSegment?: () => void;
  onEditSegment?: () => void;
  onAcceptSuggestions?: () => void;
  onRejectSuggestions?: () => void;
  onSaveEdit?: () => void;
  onCancelEdit?: () => void;
  onJumpToSegment?: (time: number) => void;
  onSeekForward?: () => void;
  onSeekBackward?: () => void;
  onToggleFullscreen?: () => void;
  onExport?: () => void;
  onHelp?: () => void;
  isEditing?: boolean;
  disabled?: boolean;
}

export const useKeyboardShortcuts = (config: KeyboardShortcutsConfig) => {
  const {
    onPlayPause,
    onNextSegment,
    onPreviousSegment,
    onEditSegment,
    onAcceptSuggestions,
    onRejectSuggestions,
    onSaveEdit,
    onCancelEdit,
    onJumpToSegment,
    onSeekForward,
    onSeekBackward,
    onToggleFullscreen,
    onExport,
    onHelp,
    isEditing = false,
    disabled = false,
  } = config;

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Don't handle shortcuts if disabled or if user is typing in an input
      if (disabled || event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        // Allow some shortcuts even when editing
        if (isEditing) {
          if (event.key === 'Enter' && event.ctrlKey && onSaveEdit) {
            event.preventDefault();
            onSaveEdit();
            return;
          }
          if (event.key === 'Escape' && onCancelEdit) {
            event.preventDefault();
            onCancelEdit();
            return;
          }
        }
        return;
      }

      // Prevent default behavior for our shortcuts
      const shouldPreventDefault = [
        'Space',
        'ArrowUp',
        'ArrowDown',
        'ArrowLeft',
        'ArrowRight',
        'F2',
        'Enter',
        'Escape',
        'KeyF',
        'KeyS',
        'KeyH',
        'Slash',
        'Digit1', 'Digit2', 'Digit3', 'Digit4', 'Digit5',
        'Digit6', 'Digit7', 'Digit8', 'Digit9',
      ].includes(event.code) ||
      (event.ctrlKey && ['KeyF', 'KeyS', 'KeyH', 'ArrowLeft', 'ArrowRight'].includes(event.code));

      if (shouldPreventDefault) {
        event.preventDefault();
      }

      switch (event.code) {
        case 'Space':
          // Play/Pause video
          if (onPlayPause) {
            onPlayPause();
          }
          break;

        case 'ArrowDown':
          // Next segment
          if (onNextSegment) {
            onNextSegment();
          }
          break;

        case 'ArrowUp':
          // Previous segment
          if (onPreviousSegment) {
            onPreviousSegment();
          }
          break;

        case 'ArrowLeft':
          // Seek backward (with Ctrl) or previous segment
          if (event.ctrlKey && onSeekBackward) {
            onSeekBackward();
          } else if (onPreviousSegment) {
            onPreviousSegment();
          }
          break;

        case 'ArrowRight':
          // Seek forward (with Ctrl) or next segment
          if (event.ctrlKey && onSeekForward) {
            onSeekForward();
          } else if (onNextSegment) {
            onNextSegment();
          }
          break;

        case 'F2':
          // Edit current segment
          if (onEditSegment) {
            onEditSegment();
          }
          break;

        case 'Enter':
          // Accept suggestions (only if not editing)
          if (!isEditing && onAcceptSuggestions) {
            onAcceptSuggestions();
          }
          break;

        case 'Escape':
          // Reject suggestions (only if not editing)
          if (!isEditing && onRejectSuggestions) {
            onRejectSuggestions();
          }
          break;

        default:
          break;

        case 'KeyF':
          // Toggle fullscreen (with Ctrl)
          if (event.ctrlKey && onToggleFullscreen) {
            onToggleFullscreen();
          }
          break;

        case 'KeyS':
          // Export/Save (with Ctrl)
          if (event.ctrlKey && onExport) {
            onExport();
          }
          break;

        case 'KeyH':
          // Help (with Ctrl)
          if (event.ctrlKey && onHelp) {
            onHelp();
          }
          break;

        case 'Slash':
          // Help (with ?)
          if (event.shiftKey && onHelp) {
            onHelp();
          }
          break;

        case 'Digit1':
        case 'Digit2':
        case 'Digit3':
        case 'Digit4':
        case 'Digit5':
        case 'Digit6':
        case 'Digit7':
        case 'Digit8':
        case 'Digit9':
          // Number keys for jumping to segments (1-9)
          const segmentIndex = parseInt(event.code.replace('Digit', '')) - 1;
          if (onJumpToSegment) {
            onJumpToSegment(segmentIndex);
          }
          break;
      }
    },
    [
      disabled,
      isEditing,
      onPlayPause,
      onNextSegment,
      onPreviousSegment,
      onEditSegment,
      onAcceptSuggestions,
      onRejectSuggestions,
      onSaveEdit,
      onCancelEdit,
      onJumpToSegment,
      onSeekForward,
      onSeekBackward,
      onToggleFullscreen,
      onExport,
      onHelp,
    ]
  );

  useEffect(() => {
    if (disabled) return;

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, disabled]);

  return {
    // Return some utility functions if needed
    isShortcutActive: !disabled,
  };
};

export default useKeyboardShortcuts;
