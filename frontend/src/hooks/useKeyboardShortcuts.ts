import { useEffect, useCallback } from 'react';

interface KeyboardShortcutsConfig {
  onPlayPause?: () => void;
  onNextSegment?: () => void;
  onPreviousSegment?: () => void;
  onEditSegment?: () => void;
  onAcceptSuggestions?: () => void;
  onRejectSuggestions?: () => void;
  onSaveEdit?: () => void;
  onCancelEdit?: () => void;
  isEditing?: boolean;
  disabled?: boolean;
}

export const useKeyboardShortcuts = (config: KeyboardShortcutsConfig) => {
  const {
    onPlayPause,
    onNextSegment,
    onPreviousSegment,
    onEditSegment,
    onAcceptSuggestions,
    onRejectSuggestions,
    onSaveEdit,
    onCancelEdit,
    isEditing = false,
    disabled = false,
  } = config;

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Don't handle shortcuts if disabled or if user is typing in an input
      if (disabled || event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        // Allow some shortcuts even when editing
        if (isEditing) {
          if (event.key === 'Enter' && event.ctrlKey && onSaveEdit) {
            event.preventDefault();
            onSaveEdit();
            return;
          }
          if (event.key === 'Escape' && onCancelEdit) {
            event.preventDefault();
            onCancelEdit();
            return;
          }
        }
        return;
      }

      // Prevent default behavior for our shortcuts
      const shouldPreventDefault = [
        'Space',
        'ArrowUp',
        'ArrowDown',
        'ArrowLeft',
        'ArrowRight',
        'F2',
        'Enter',
        'Escape',
      ].includes(event.code);

      if (shouldPreventDefault) {
        event.preventDefault();
      }

      switch (event.code) {
        case 'Space':
          // Play/Pause video
          if (onPlayPause) {
            onPlayPause();
          }
          break;

        case 'ArrowDown':
        case 'ArrowRight':
          // Next segment
          if (onNextSegment) {
            onNextSegment();
          }
          break;

        case 'ArrowUp':
        case 'ArrowLeft':
          // Previous segment
          if (onPreviousSegment) {
            onPreviousSegment();
          }
          break;

        case 'F2':
          // Edit current segment
          if (onEditSegment) {
            onEditSegment();
          }
          break;

        case 'Enter':
          // Accept suggestions (only if not editing)
          if (!isEditing && onAcceptSuggestions) {
            onAcceptSuggestions();
          }
          break;

        case 'Escape':
          // Reject suggestions (only if not editing)
          if (!isEditing && onRejectSuggestions) {
            onRejectSuggestions();
          }
          break;

        default:
          break;
      }
    },
    [
      disabled,
      isEditing,
      onPlayPause,
      onNextSegment,
      onPreviousSegment,
      onEditSegment,
      onAcceptSuggestions,
      onRejectSuggestions,
      onSaveEdit,
      onCancelEdit,
    ]
  );

  useEffect(() => {
    if (disabled) return;

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, disabled]);

  return {
    // Return some utility functions if needed
    isShortcutActive: !disabled,
  };
};

export default useKeyboardShortcuts;
