import React, { useState, useEffect } from 'react';
import { X, Download, Settings, FileText, Globe, CheckSquare, Square } from 'lucide-react';
import { projectsApi } from '../services/api';

interface ExportOptions {
  format: string;
  encoding: string;
  include_original: boolean;
  include_timestamps: boolean;
  selected_segments?: string[];
  custom_options: Record<string, any>;
  return_content: boolean;
}

interface ExportOptionsInfo {
  supported_formats: string[];
  supported_encodings: string[];
  project_info: {
    project_id: string;
    title: string;
    segments_count: number;
    status: string;
  };
  format_descriptions: Record<string, string>;
  encoding_descriptions: Record<string, string>;
}

interface AdvancedExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  segments: any[];
}

const AdvancedExportDialog: React.FC<AdvancedExportDialogProps> = ({
  isOpen,
  onClose,
  projectId,
  segments
}) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'srt',
    encoding: 'utf-8',
    include_original: false,
    include_timestamps: true,
    selected_segments: undefined,
    custom_options: {},
    return_content: false
  });

  const [optionsInfo, setOptionsInfo] = useState<ExportOptionsInfo | null>(null);
  const [selectedSegments, setSelectedSegments] = useState<Set<string>>(new Set());
  const [isExporting, setIsExporting] = useState(false);
  const [showSegmentSelection, setShowSegmentSelection] = useState(false);

  useEffect(() => {
    if (isOpen && projectId) {
      loadExportOptions();
    }
  }, [isOpen, projectId]);

  const loadExportOptions = async () => {
    try {
      const response = await fetch(`http://localhost:9000/api/v1/projects/${projectId}/export-options`);
      const data = await response.json();
      setOptionsInfo(data);
    } catch (error) {
      console.error('Failed to load export options:', error);
    }
  };

  const handleExport = async () => {
    setIsExporting(true);
    try {
      const options = {
        ...exportOptions,
        selected_segments: showSegmentSelection && selectedSegments.size > 0 
          ? Array.from(selectedSegments) 
          : undefined
      };

      const response = await fetch(`http://localhost:9000/api/v1/projects/${projectId}/export-advanced`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options)
      });

      if (options.return_content) {
        const result = await response.json();
        // Zobraz preview nebo ulož do clipboardu
        navigator.clipboard.writeText(result.content);
        alert('Obsah byl zkopírován do schránky!');
      } else {
        // Stáhni soubor
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `export_${projectId}.${exportOptions.format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }

      onClose();
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export se nezdařil. Zkuste to znovu.');
    } finally {
      setIsExporting(false);
    }
  };

  const toggleSegmentSelection = (segmentId: string) => {
    const newSelection = new Set(selectedSegments);
    if (newSelection.has(segmentId)) {
      newSelection.delete(segmentId);
    } else {
      newSelection.add(segmentId);
    }
    setSelectedSegments(newSelection);
  };

  const selectAllSegments = () => {
    setSelectedSegments(new Set(segments.map(s => s.segment_id)));
  };

  const clearSelection = () => {
    setSelectedSegments(new Set());
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <Download className="w-5 h-5 mr-2" />
            Pokročilý export
          </h2>
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Formát */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FileText className="w-4 h-4 inline mr-1" />
              Formát exportu
            </label>
            <select
              value={exportOptions.format}
              onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {optionsInfo?.supported_formats.map(format => (
                <option key={format} value={format}>
                  {format.toUpperCase()} - {optionsInfo.format_descriptions[format]}
                </option>
              ))}
            </select>
          </div>

          {/* Kódování */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Globe className="w-4 h-4 inline mr-1" />
              Kódování textu
            </label>
            <select
              value={exportOptions.encoding}
              onChange={(e) => setExportOptions(prev => ({ ...prev, encoding: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {optionsInfo?.supported_encodings.map(encoding => (
                <option key={encoding} value={encoding}>
                  {encoding.toUpperCase()} - {optionsInfo.encoding_descriptions[encoding]}
                </option>
              ))}
            </select>
          </div>

          {/* Možnosti obsahu */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              <Settings className="w-4 h-4 inline mr-1" />
              Možnosti obsahu
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.include_original}
                  onChange={(e) => setExportOptions(prev => ({ 
                    ...prev, 
                    include_original: e.target.checked 
                  }))}
                  className="mr-2"
                />
                Zahrnout původní text
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.include_timestamps}
                  onChange={(e) => setExportOptions(prev => ({ 
                    ...prev, 
                    include_timestamps: e.target.checked 
                  }))}
                  className="mr-2"
                />
                Zahrnout časové značky
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.return_content}
                  onChange={(e) => setExportOptions(prev => ({ 
                    ...prev, 
                    return_content: e.target.checked 
                  }))}
                  className="mr-2"
                />
                Zkopírovat do schránky místo stažení
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={showSegmentSelection}
                  onChange={(e) => setShowSegmentSelection(e.target.checked)}
                  className="mr-2"
                />
                Exportovat pouze vybrané segmenty
              </label>
            </div>
          </div>

          {/* Výběr segmentů */}
          {showSegmentSelection && (
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-gray-700">
                  Výběr segmentů ({selectedSegments.size}/{segments.length})
                </label>
                <div className="space-x-2">
                  <button
                    onClick={selectAllSegments}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    Vybrat vše
                  </button>
                  <button
                    onClick={clearSelection}
                    className="text-xs text-gray-600 hover:text-gray-800"
                  >
                    Zrušit výběr
                  </button>
                </div>
              </div>
              
              <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md p-2">
                {segments.map((segment) => (
                  <div
                    key={segment.segment_id}
                    className="flex items-center p-2 hover:bg-gray-50 cursor-pointer"
                    onClick={() => toggleSegmentSelection(segment.segment_id)}
                  >
                    {selectedSegments.has(segment.segment_id) ? (
                      <CheckSquare className="w-4 h-4 text-blue-600 mr-2" />
                    ) : (
                      <Square className="w-4 h-4 text-gray-400 mr-2" />
                    )}
                    <span className="text-sm text-gray-600 mr-2">
                      #{segment.sequence_number}
                    </span>
                    <span className="text-sm text-gray-900 truncate">
                      {segment.corrected_text || segment.original_text}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Informace o projektu */}
          {optionsInfo && (
            <div className="bg-gray-50 p-4 rounded-md">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Informace o projektu</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>Název: {optionsInfo.project_info.title}</div>
                <div>Počet segmentů: {optionsInfo.project_info.segments_count}</div>
                <div>Status: {optionsInfo.project_info.status}</div>
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Zrušit
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Exportuji...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Exportovat
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdvancedExportDialog;
