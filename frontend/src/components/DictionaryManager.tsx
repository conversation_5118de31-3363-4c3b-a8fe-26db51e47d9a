import React, { useState, useEffect } from 'react';
import { dictionaryApi } from '../services/api';
import { UserDictionaryTerm } from '../types';
import { Plus, Trash2, Loader2 } from 'lucide-react';

const DictionaryManager: React.FC = () => {
  const [terms, setTerms] = useState<UserDictionaryTerm[]>([]);
  const [newPhrase, setNewPhrase] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(true);
  const [loading, setLoading] = useState(false);
  const [isAdding, setIsAdding] = useState(false);

  useEffect(() => {
    loadTerms();
  }, []);

  const loadTerms = async () => {
    setLoading(true);
    try {
      const termsList = await dictionaryApi.getTerms();
      setTerms(termsList);
    } catch (error) {
      console.error('Failed to load dictionary terms:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddTerm = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newPhrase.trim()) return;

    setIsAdding(true);
    try {
      const newTerm = await dictionaryApi.addTerm(newPhrase.trim(), caseSensitive);
      setTerms([newTerm, ...terms]);
      setNewPhrase('');
      setCaseSensitive(true);
    } catch (error) {
      console.error('Failed to add term:', error);
      alert('Nepodařilo se přidat termín. Možná již existuje.');
    } finally {
      setIsAdding(false);
    }
  };

  const handleDeleteTerm = async (termId: string) => {
    if (!window.confirm('Opravdu chcete smazat tento termín?')) return;

    try {
      await dictionaryApi.deleteTerm(termId);
      setTerms(terms.filter((term) => term.term_id !== termId));
    } catch (error) {
      console.error('Failed to delete term:', error);
      alert('Nepodařilo se smazat termín.');
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Uživatelský slovník</h3>
        <p className="text-sm text-gray-600 mt-1">
          Termíny v slovníku budou při korekci titulků respektovány jako vlastní jména.
        </p>
      </div>

      <div className="flex-1 overflow-y-auto">
        {/* Add Term Form */}
        <div className="p-4 border-b border-gray-200">
          <form onSubmit={handleAddTerm} className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nový termín
              </label>
              <input
                type="text"
                value={newPhrase}
                onChange={(e) => setNewPhrase(e.target.value)}
                placeholder="Zadejte termín..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isAdding}
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="case-sensitive"
                checked={caseSensitive}
                onChange={(e) => setCaseSensitive(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                disabled={isAdding}
              />
              <label
                htmlFor="case-sensitive"
                className="ml-2 block text-sm text-gray-700"
              >
                Rozlišovat velikost písmen
              </label>
            </div>
            <button
              type="submit"
              className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
              disabled={isAdding || !newPhrase.trim()}
            >
              {isAdding ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Přidává se...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  Přidat termín
                </>
              )}
            </button>
          </form>
        </div>

        {/* Terms List */}
        <div className="p-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
            </div>
          ) : terms.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">Žádné termíny ve slovníku.</p>
            </div>
          ) : (
            <div className="space-y-2">
              {terms.map((term) => (
                <div
                  key={term.term_id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      {term.phrase}
                    </div>
                    <div className="text-sm text-gray-600">
                      {term.case_sensitive
                        ? 'Rozlišuje velikost písmen'
                        : 'Nerozlišuje velikost písmen'}
                    </div>
                  </div>
                  <button
                    onClick={() => handleDeleteTerm(term.term_id)}
                    className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors"
                    title="Smazat termín"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DictionaryManager;