import React, { useState } from 'react';
import { Keyboard, X } from 'lucide-react';

const KeyboardShortcutsHelp: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const shortcuts = [
    {
      category: 'Přehrávání',
      items: [
        { key: 'Space', description: '<PERSON><PERSON><PERSON><PERSON>á<PERSON>/Pozastavit video' },
      ],
    },
    {
      category: 'Navigace',
      items: [
        { key: '↑ / ←', description: 'Předchozí segment' },
        { key: '↓ / →', description: 'Následující segment' },
      ],
    },
    {
      category: 'Editace',
      items: [
        { key: 'F2', description: 'Editovat aktivní segment' },
        { key: 'Ctrl + Enter', description: 'Uložit změny (při editaci)' },
        { key: 'Esc', description: 'Zrušit editaci / Zamítnout návrhy' },
      ],
    },
    {
      category: 'Návrhy AI',
      items: [
        { key: 'Enter', description: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> všechny návrhy aktivního segmentu' },
        { key: 'Esc', description: 'Zamítnout všechny návrhy aktivního segmentu' },
      ],
    },
  ];

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 p-3 bg-gray-800 text-white rounded-full shadow-lg hover:bg-gray-700 transition-colors z-50"
        title="Zobrazit klávesové zkratky"
      >
        <Keyboard className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <Keyboard className="w-5 h-5 mr-2" />
            Klávesové zkratky
          </h2>
          <button
            onClick={() => setIsOpen(false)}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-4 space-y-6">
          {shortcuts.map((category) => (
            <div key={category.category}>
              <h3 className="text-sm font-medium text-gray-900 mb-3">
                {category.category}
              </h3>
              <div className="space-y-2">
                {category.items.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between py-2"
                  >
                    <span className="text-sm text-gray-600">
                      {item.description}
                    </span>
                    <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 border border-gray-300 rounded">
                      {item.key}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <p className="text-xs text-gray-500 text-center">
            Klávesové zkratky fungují pouze když není aktivní textové pole.
          </p>
        </div>
      </div>
    </div>
  );
};

export default KeyboardShortcutsHelp;
