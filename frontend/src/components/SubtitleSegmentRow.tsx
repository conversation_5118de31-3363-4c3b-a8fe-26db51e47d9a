import React, { useState, useRef, useEffect } from 'react';
import { SubtitleSegment } from '../types';
import { Check, X, Edit3 } from 'lucide-react';
import SuggestionPanel from './SuggestionPanel';

interface SubtitleSegmentRowProps {
  segment: SubtitleSegment;
  isActive: boolean;
  onSegmentClick: (segmentId: string) => void;
  onTextChange: (segmentId: string, newText: string) => void;
  onApplySuggestion?: (segmentId: string, suggestionId: string) => void;
  onRejectSuggestion?: (segmentId: string, suggestionId: string) => void;
  onEditingChange?: (isEditing: boolean) => void;
}

const SubtitleSegmentRow: React.FC<SubtitleSegmentRowProps> = ({
  segment,
  isActive,
  onSegmentClick,
  onTextChange,
  onApplySuggestion,
  onRejectSuggestion,
  onEditingChange,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(segment.corrected_text);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  // Simple diff implementation for highlighting changes
  const createSimpleDiff = (original: string, corrected: string) => {
    const originalWords = original.split(' ');
    const correctedWords = corrected.split(' ');
    const maxLength = Math.max(originalWords.length, correctedWords.length);

    const originalParts = [];
    const correctedParts = [];

    for (let i = 0; i < maxLength; i++) {
      const origWord = originalWords[i] || '';
      const corrWord = correctedWords[i] || '';

      if (origWord !== corrWord) {
        if (origWord) {
          originalParts.push({ value: origWord + ' ', removed: true });
        }
        if (corrWord) {
          correctedParts.push({ value: corrWord + ' ', added: true });
        }
      } else if (origWord) {
        originalParts.push({ value: origWord + ' ', unchanged: true });
        correctedParts.push({ value: corrWord + ' ', unchanged: true });
      }
    }

    return { originalParts, correctedParts };
  };

  useEffect(() => {
    if (isEditing && textAreaRef.current) {
      textAreaRef.current.focus();
      textAreaRef.current.select();
    }
  }, [isEditing]);

  const formatTime = (timeMs: number): string => {
    const seconds = Math.floor(timeMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    const ms = timeMs % 1000;

    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
      .toString()
      .padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
  };

  const handleSave = () => {
    onTextChange(segment.segment_id, editText);
    setIsEditing(false);
    onEditingChange?.(false);
  };

  const handleCancel = () => {
    setEditText(segment.corrected_text);
    setIsEditing(false);
    onEditingChange?.(false);
  };

  const handleStartEditing = () => {
    setIsEditing(true);
    onEditingChange?.(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const handleApplySuggestion = (suggestionId: string) => {
    if (onApplySuggestion) {
      onApplySuggestion(segment.segment_id, suggestionId);
    }
  };

  const handleRejectSuggestion = (suggestionId: string) => {
    if (onRejectSuggestion) {
      onRejectSuggestion(segment.segment_id, suggestionId);
    }
  };

  const handleApplyAllSuggestions = () => {
    segment.suggestions.forEach(suggestion => {
      if (!suggestion.applied && onApplySuggestion) {
        onApplySuggestion(segment.segment_id, suggestion.suggestion_id);
      }
    });
  };

  const handleRejectAllSuggestions = () => {
    segment.suggestions.forEach(suggestion => {
      if (!suggestion.applied && onRejectSuggestion) {
        onRejectSuggestion(segment.segment_id, suggestion.suggestion_id);
      }
    });
  };

  const getStatusClassName = () => {
    switch (segment.status) {
      case 'needs_review':
        return 'border-orange-200 bg-orange-50';
      case 'auto_corrected':
        return 'border-green-200 bg-green-50';
      case 'user_modified':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200';
    }
  };

  const getStatusColor = () => {
    switch (segment.status) {
      case 'needs_review':
        return 'text-orange-600';
      case 'auto_corrected':
        return 'text-green-600';
      case 'user_modified':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  const renderTextDiff = () => {
    if (segment.original_text === segment.corrected_text) {
      return <span className="text-gray-900">{segment.corrected_text}</span>;
    }

    // Simple diff highlighting
    const { originalParts, correctedParts } = createSimpleDiff(segment.original_text, segment.corrected_text);

    return (
      <div className="space-y-2">
        {/* Show original text with strikethrough for removed parts */}
        <div className="text-sm">
          <span className="text-gray-500 text-xs uppercase tracking-wide font-medium">Původní:</span>
          <div className="mt-1">
            {originalParts.map((part, index) => {
              if (part.removed) {
                return (
                  <span
                    key={index}
                    className="bg-red-100 text-red-800 line-through px-1 rounded"
                    title="Odstraněno"
                  >
                    {part.value}
                  </span>
                );
              } else {
                return (
                  <span key={index} className="text-gray-700">
                    {part.value}
                  </span>
                );
              }
            })}
          </div>
        </div>

        {/* Show corrected text with highlighting for added parts */}
        <div className="text-sm">
          <span className="text-gray-500 text-xs uppercase tracking-wide font-medium">Opraveno:</span>
          <div className="mt-1">
            {correctedParts.map((part, index) => {
              if (part.added) {
                return (
                  <span
                    key={index}
                    className="bg-green-100 text-green-800 px-1 rounded font-medium"
                    title="Přidáno/Opraveno"
                  >
                    {part.value}
                  </span>
                );
              } else {
                return (
                  <span key={index} className="text-gray-900">
                    {part.value}
                  </span>
                );
              }
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      className={`p-4 cursor-pointer transition-all ${
        isActive ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50'
      } ${getStatusClassName()}`}
      onClick={() => onSegmentClick(segment.segment_id)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-sm font-mono text-gray-600">
              {formatTime(segment.start_time_ms)}
            </span>
            <span className="text-gray-400">→</span>
            <span className="text-sm font-mono text-gray-600">
              {formatTime(segment.end_time_ms)}
            </span>
            <span
              className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()} bg-opacity-10`}
            >
              {segment.status}
            </span>
          </div>

          {isEditing ? (
            <div className="space-y-2">
              <textarea
                ref={textAreaRef}
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                rows={2}
                onClick={(e) => e.stopPropagation()}
              />
              <div className="flex items-center space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSave();
                  }}
                  className="p-1 text-green-600 hover:bg-green-100 rounded"
                >
                  <Check className="w-4 h-4" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCancel();
                  }}
                  className="p-1 text-red-600 hover:bg-red-100 rounded"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div
                className="text-gray-900 cursor-text"
                onDoubleClick={(e) => {
                  e.stopPropagation();
                  handleStartEditing();
                }}
              >
                {renderTextDiff()}
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleStartEditing();
                }}
                className="p-1 text-gray-400 hover:text-gray-600"
                title="Editovat text (F2)"
              >
                <Edit3 className="w-4 h-4" />
              </button>
            </div>
          )}

          <SuggestionPanel
            suggestions={segment.suggestions}
            onApplySuggestion={handleApplySuggestion}
            onRejectSuggestion={handleRejectSuggestion}
            onApplyAll={handleApplyAllSuggestions}
            onRejectAll={handleRejectAllSuggestions}
          />
        </div>
      </div>
    </div>
  );
};

export default SubtitleSegmentRow;