import React, { useState } from 'react';
import { CorrectionSuggestion } from '../types';
import { Check, X, AlertCircle, Lightbulb, Edit, Type, Zap } from 'lucide-react';

interface SuggestionPanelProps {
  suggestions: CorrectionSuggestion[];
  onApplySuggestion: (suggestionId: string) => void;
  onRejectSuggestion: (suggestionId: string) => void;
  onApplyAll: () => void;
  onRejectAll: () => void;
}

const SuggestionPanel: React.FC<SuggestionPanelProps> = ({
  suggestions,
  onApplySuggestion,
  onRejectSuggestion,
  onApplyAll,
  onRejectAll,
}) => {
  const [expandedSuggestion, setExpandedSuggestion] = useState<string | null>(null);

  if (suggestions.length === 0) {
    return null;
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'dictionary_match':
        return <Lightbulb className="w-4 h-4" />;
      case 'punctuation':
        return <Type className="w-4 h-4" />;
      case 'capitalization':
        return <Edit className="w-4 h-4" />;
      case 'semantic_rewrite':
        return <Zap className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'dictionary_match':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'punctuation':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'capitalization':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'semantic_rewrite':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'dictionary_match':
        return 'Slovník';
      case 'punctuation':
        return 'Interpunkce';
      case 'capitalization':
        return 'Velká písmena';
      case 'semantic_rewrite':
        return 'Sémantika';
      default:
        return type;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const pendingSuggestions = suggestions.filter(s => !s.applied);
  const appliedSuggestions = suggestions.filter(s => s.applied);

  return (
    <div className="mt-4 border-t border-gray-200 pt-4">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-medium text-gray-900 flex items-center">
          <AlertCircle className="w-4 h-4 mr-2 text-orange-500" />
          Návrhy AI ({pendingSuggestions.length})
        </h4>
        
        {pendingSuggestions.length > 1 && (
          <div className="flex space-x-2">
            <button
              onClick={onApplyAll}
              className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              title="Přijmout všechny návrhy"
            >
              <Check className="w-3 h-3" />
            </button>
            <button
              onClick={onRejectAll}
              className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              title="Zamítnout všechny návrhy"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        )}
      </div>

      <div className="space-y-2">
        {pendingSuggestions.map((suggestion) => (
          <div
            key={suggestion.suggestion_id}
            className={`border rounded-lg p-3 transition-all ${
              expandedSuggestion === suggestion.suggestion_id
                ? 'border-blue-300 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getTypeColor(suggestion.type)}`}>
                    {getTypeIcon(suggestion.type)}
                    <span className="ml-1">{getTypeLabel(suggestion.type)}</span>
                  </div>
                  
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(suggestion.confidence)}`}>
                    {Math.round(suggestion.confidence * 100)}%
                  </div>
                </div>

                <div className="text-sm mb-2">
                  <span className="text-red-600 line-through bg-red-50 px-1 rounded">
                    "{suggestion.original_fragment}"
                  </span>
                  <span className="mx-2 text-gray-400">→</span>
                  <span className="text-green-600 bg-green-50 px-1 rounded font-medium">
                    "{suggestion.suggested_fragment}"
                  </span>
                </div>

                {suggestion.description && (
                  <p className="text-xs text-gray-600 mb-2">
                    {suggestion.description}
                  </p>
                )}

                {expandedSuggestion === suggestion.suggestion_id && (
                  <div className="mt-2 p-2 bg-white rounded border text-xs">
                    <div className="grid grid-cols-2 gap-2 text-gray-600">
                      <div>
                        <span className="font-medium">Typ:</span> {getTypeLabel(suggestion.type)}
                      </div>
                      <div>
                        <span className="font-medium">Jistota:</span> {Math.round(suggestion.confidence * 100)}%
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-1 ml-3">
                <button
                  onClick={() => setExpandedSuggestion(
                    expandedSuggestion === suggestion.suggestion_id ? null : suggestion.suggestion_id
                  )}
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Zobrazit detaily"
                >
                  <AlertCircle className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => onApplySuggestion(suggestion.suggestion_id)}
                  className="p-1 text-green-600 hover:bg-green-100 rounded transition-colors"
                  title="Přijmout návrh (Enter)"
                >
                  <Check className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => onRejectSuggestion(suggestion.suggestion_id)}
                  className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors"
                  title="Zamítnout návrh (Esc)"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {appliedSuggestions.length > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-100">
          <h5 className="text-xs font-medium text-gray-500 mb-2">
            Aplikované návrhy ({appliedSuggestions.length})
          </h5>
          <div className="space-y-1">
            {appliedSuggestions.map((suggestion) => (
              <div
                key={suggestion.suggestion_id}
                className="text-xs text-gray-500 bg-gray-50 p-2 rounded"
              >
                <span className="font-medium">{getTypeLabel(suggestion.type)}:</span>
                <span className="ml-1">
                  "{suggestion.original_fragment}" → "{suggestion.suggested_fragment}"
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SuggestionPanel;
