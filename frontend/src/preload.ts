import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

contextBridge.exposeInMainWorld('electronAPI', {
  // Menu events
  onMenuNewProject: (callback: () => void) => {
    ipcRenderer.on('menu-new-project', callback);
  },
  onMenuExportSRT: (callback: () => void) => {
    ipcRenderer.on('menu-export-srt', callback);
  },

  // App info
  getAppVersion: () => ipcRenderer.invoke('app-version'),

  // Remove listeners
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
});

declare global {
  interface Window {
    electronAPI: {
      onMenuNewProject: (callback: () => void) => void;
      onMenuExportSRT: (callback: () => void) => void;
      getAppVersion: () => Promise<string>;
      removeAllListeners: (channel: string) => void;
    };
  }
}