export interface Project {
  project_id: string;
  youtube_url: string;
  video_title?: string;
  video_duration?: number;
  status: string;
  created_at: string;
  updated_at: string;
  error_message?: string;
  segments: SubtitleSegment[];
}

export interface ProjectSummary {
  project_id: string;
  youtube_url: string;
  video_title?: string;
  video_duration?: number;
  status: string;
  created_at: string;
  updated_at: string;
  error_message?: string;
}

export interface SubtitleSegment {
  segment_id: string;
  sequence_number: number;
  start_time_ms: number;
  end_time_ms: number;
  original_text: string;
  corrected_text: string;
  status: string;
  suggestions: CorrectionSuggestion[];
}

export interface CorrectionSuggestion {
  suggestion_id: string;
  type: string;
  confidence: number;
  description?: string;
  original_fragment: string;
  suggested_fragment: string;
  applied: boolean;
}

export interface UserDictionaryTerm {
  term_id: string;
  phrase: string;
  case_sensitive: boolean;
  created_at: string;
}

export interface CreateProjectRequest {
  youtube_url: string;
}

export interface UpdateSegmentRequest {
  corrected_text: string;
}

export interface CreateDictionaryTermRequest {
  phrase: string;
  case_sensitive: boolean;
}