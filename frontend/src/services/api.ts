import axios from 'axios';
import {
  Project,
  ProjectSummary,
  SubtitleSegment,
  UserDictionaryTerm,
  CreateProjectRequest,
  UpdateSegmentRequest,
  CreateDictionaryTermRequest,
} from '../types';

const API_BASE_URL = 'http://localhost:9000/api/v1';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for error handling
apiClient.interceptors.request.use(
  (config) => config,
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const projectsApi = {
  async createProject(youtubeUrl: string): Promise<Project> {
    const response = await apiClient.post<Project>('/projects', {
      youtube_url: youtubeUrl,
    });
    return response.data;
  },

  async getProjects(): Promise<ProjectSummary[]> {
    const response = await apiClient.get<ProjectSummary[]>('/projects');
    return response.data;
  },

  async getProject(projectId: string): Promise<Project> {
    const response = await apiClient.get<Project>(`/projects/${projectId}`);
    return response.data;
  },

  async updateSegment(
    projectId: string,
    segmentId: string,
    correctedText: string
  ): Promise<SubtitleSegment> {
    const response = await apiClient.put<SubtitleSegment>(
      `/projects/${projectId}/segments/${segmentId}`,
      { corrected_text: correctedText } as UpdateSegmentRequest
    );
    return response.data;
  },

  async applySuggestion(
    projectId: string,
    segmentId: string,
    suggestionId: string
  ): Promise<void> {
    await apiClient.post(
      `/projects/${projectId}/segments/${segmentId}/suggestions/${suggestionId}/apply`
    );
  },

  async getExportPreview(projectId: string): Promise<any> {
    const response = await apiClient.get(`/projects/${projectId}/export/preview`);
    return response.data;
  },

  async exportProject(projectId: string): Promise<Blob> {
    const response = await apiClient.post(
      `/projects/${projectId}/export`,
      {},
      { responseType: 'blob' }
    );
    return response.data;
  },

  // Nové API metody pro manuální akce
  async getAvailableSubtitles(projectId: string): Promise<any> {
    const response = await apiClient.get(`/projects/${projectId}/available-subtitles`);
    return response.data;
  },

  async extractSubtitles(projectId: string, language: string = 'cs'): Promise<any> {
    const response = await apiClient.post(`/projects/${projectId}/extract-subtitles?language=${language}`);
    return response.data;
  },

  async downloadAudio(projectId: string): Promise<any> {
    const response = await apiClient.post(`/projects/${projectId}/download-audio`);
    return response.data;
  },

  async transcribeWithWhisper(projectId: string): Promise<any> {
    const response = await apiClient.post(`/projects/${projectId}/transcribe-whisper`);
    return response.data;
  },

  async correctWithAI(projectId: string): Promise<any> {
    const response = await apiClient.post(`/projects/${projectId}/correct-ai`);
    return response.data;
  },

  async deleteProject(projectId: string): Promise<void> {
    await apiClient.delete(`/projects/${projectId}`);
  },
};

export const dictionaryApi = {
  async getTerms(): Promise<UserDictionaryTerm[]> {
    const response = await apiClient.get<UserDictionaryTerm[]>('/dictionary');
    return response.data;
  },

  async addTerm(phrase: string, caseSensitive: boolean): Promise<UserDictionaryTerm> {
    const response = await apiClient.post<UserDictionaryTerm>('/dictionary', {
      phrase,
      case_sensitive: caseSensitive,
    } as CreateDictionaryTermRequest);
    return response.data;
  },

  async deleteTerm(termId: string): Promise<void> {
    await apiClient.delete(`/dictionary/${termId}`);
  },
};

export default apiClient;